import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaStar, FaClock, FaMapMarkerAlt } from 'react-icons/fa';
import ZoneUserLayout from './ZoneUserLayout';
import ZoneUserNavbar from './ZoneUserNavbar';

// Mock zone shops data
const mockZoneShops = [
  {
    id: 'shop1',
    name: 'Pizza Corner',
    cuisine: 'Italian',
    rating: 4.8,
    deliveryTime: '15-20 min',
    image: '/api/placeholder/300/200',
    description: 'Authentic Italian pizzas made with fresh ingredients',
    isOpen: true,
    distance: '50m'
  },
  {
    id: 'shop2',
    name: 'Burger Junction',
    cuisine: 'American',
    rating: 4.6,
    deliveryTime: '10-15 min',
    image: '/api/placeholder/300/200',
    description: 'Juicy burgers and crispy fries',
    isOpen: true,
    distance: '30m'
  },
  {
    id: 'shop3',
    name: 'Spice Garden',
    cuisine: 'Indian',
    rating: 4.7,
    deliveryTime: '20-25 min',
    image: '/api/placeholder/300/200',
    description: 'Traditional Indian curries and biryanis',
    isOpen: true,
    distance: '40m'
  },
  {
    id: 'shop4',
    name: 'Noodle House',
    cuisine: 'Chinese',
    rating: 4.5,
    deliveryTime: '15-20 min',
    image: '/api/placeholder/300/200',
    description: 'Fresh noodles and authentic Chinese dishes',
    isOpen: false,
    distance: '60m'
  },
  {
    id: 'shop5',
    name: 'Taco Fiesta',
    cuisine: 'Mexican',
    rating: 4.4,
    deliveryTime: '12-18 min',
    image: '/api/placeholder/300/200',
    description: 'Spicy tacos and fresh burritos',
    isOpen: true,
    distance: '45m'
  },
  {
    id: 'shop6',
    name: 'Sweet Treats',
    cuisine: 'Desserts',
    rating: 4.9,
    deliveryTime: '5-10 min',
    image: '/api/placeholder/300/200',
    description: 'Delicious cakes, pastries and ice cream',
    isOpen: true,
    distance: '25m'
  }
];

const ZoneShopSelectionScreen = () => {
  const { zoneId, tableId, userId } = useParams();
  const navigate = useNavigate();
  const [shops, setShops] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setShops(mockZoneShops);
      setLoading(false);
    }, 1000);
  }, []);

  const handleShopSelect = (shopId) => {
    navigate(`/tableserve/zone/${zoneId}/table/${tableId}/user/${userId}/shop/${shopId}/menu`);
  };

  const handleViewAllMenu = () => {
    navigate(`/tableserve/zone/${zoneId}/table/${tableId}/user/${userId}/menu`);
  };

  if (loading) {
    return (
      <ZoneUserLayout>
        <ZoneUserNavbar title="Food Zone" />
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
        </div>
      </ZoneUserLayout>
    );
  }

  return (
    <ZoneUserLayout>
      <ZoneUserNavbar title="Food Zone" />
      
      <div className="max-w-md mx-auto p-4">
        {/* Welcome Section */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-fredoka text-primary-bg mb-2">Welcome to Food Zone</h1>
          <p className="text-placeholder-subtext font-raleway">Choose from our amazing restaurants</p>
        </div>

        {/* View All Menu Button */}
        <motion.button
          whileTap={{ scale: 0.95 }}
          onClick={handleViewAllMenu}
          className="w-full bg-accent text-white py-4 rounded-xl font-raleway font-bold text-lg mb-6 shadow-lg hover:bg-hover-shade transition-colors duration-300"
        >
          🍽️ View All Menus Together
        </motion.button>

        {/* Shop List */}
        <div className="space-y-4">
          <h2 className="text-lg font-fredoka text-primary-bg mb-4">Or browse by restaurant:</h2>
          
          {shops.map((shop, index) => (
            <motion.div
              key={shop.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              onClick={() => shop.isOpen && handleShopSelect(shop.id)}
              className={`bg-white rounded-xl shadow-md overflow-hidden cursor-pointer transition-all duration-300 ${
                shop.isOpen 
                  ? 'hover:shadow-lg hover:scale-[1.02]' 
                  : 'opacity-60 cursor-not-allowed'
              }`}
            >
              <div className="relative">
                <img 
                  src={shop.image} 
                  alt={shop.name}
                  className="w-full h-32 object-cover"
                />
                {!shop.isOpen && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <span className="text-white font-bold">Currently Closed</span>
                  </div>
                )}
                <div className="absolute top-2 right-2 bg-white rounded-full px-2 py-1 flex items-center">
                  <FaStar className="text-yellow-500 text-xs mr-1" />
                  <span className="text-xs font-bold">{shop.rating}</span>
                </div>
              </div>
              
              <div className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="text-lg font-fredoka text-primary-bg">{shop.name}</h3>
                    <p className="text-sm text-placeholder-subtext">{shop.cuisine}</p>
                  </div>
                </div>
                
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{shop.description}</p>
                
                <div className="flex items-center justify-between text-xs text-placeholder-subtext">
                  <div className="flex items-center">
                    <FaClock className="mr-1" />
                    <span>{shop.deliveryTime}</span>
                  </div>
                  <div className="flex items-center">
                    <FaMapMarkerAlt className="mr-1" />
                    <span>{shop.distance}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </ZoneUserLayout>
  );
};

export default ZoneShopSelectionScreen;
