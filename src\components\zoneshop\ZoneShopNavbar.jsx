import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaBars,
  FaTimes,
  FaBell,
  FaUser,
  FaSignOutAlt,
  FaCog,
  FaSearch,
  FaShoppingCart,
  FaExclamationTriangle,
  FaCheckCircle,
  FaStore,
  FaMapMarkerAlt,
  FaClock,
  FaRupeeSign
} from 'react-icons/fa';
import { logoutUser } from '../../store/slices/authSlice';
import ThemeToggle from '../common/ThemeToggle';
import { selectTheme } from '../../store/slices/themeSlice';

const ZoneShopNavbar = ({ sidebarOpen, setSidebarOpen, isMobile }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { zoneId, shopId } = useParams();
  const { user } = useSelector((state) => state.auth);
  const currentTheme = useSelector(selectTheme);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfile, setShowProfile] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Mock notifications for zone shop vendor
  const notifications = [
    {
      id: 1,
      type: 'order',
      title: 'New Order Received',
      message: 'Table 5 - Margherita Pizza x2 - ₹450',
      time: '2 minutes ago',
      read: false,
      icon: FaShoppingCart,
      color: 'text-green-400'
    },
    {
      id: 2,
      type: 'alert',
      title: 'Low Stock Alert',
      message: 'Cheese running low - 5 portions left',
      time: '15 minutes ago',
      read: false,
      icon: FaExclamationTriangle,
      color: 'text-yellow-400'
    },
    {
      id: 3,
      type: 'success',
      title: 'Order Completed',
      message: 'Table 3 order marked as delivered',
      time: '30 minutes ago',
      read: true,
      icon: FaCheckCircle,
      color: 'text-blue-400'
    },
    {
      id: 4,
      type: 'info',
      title: 'Daily Revenue Update',
      message: 'Today\'s revenue: ₹2,450 (15 orders)',
      time: '1 hour ago',
      read: true,
      icon: FaRupeeSign,
      color: 'text-purple-400'
    }
  ];

  const unreadCount = notifications.filter(n => !n.read).length;

  const handleLogout = () => {
    dispatch(logoutUser());
    navigate('/tableserve/login');
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Implement search functionality for menu items, orders, etc.
      console.log('Searching for:', searchQuery);
    }
  };

  const handleProfileClick = () => {
    navigate(`/tableserve/zone/${zoneId}/shop/${shopId}/profile`);
    setShowProfile(false);
  };

  const handleSettingsClick = () => {
    navigate(`/tableserve/zone/${zoneId}/shop/${shopId}/settings`);
    setShowProfile(false);
  };

  return (
    <motion.nav
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="fixed top-0 right-0 left-0 admin-navbar  z-50"
      style={{ zIndex: 50 }}
    >
      <div className="flex items-center justify-between px-3 sm:px-6 py-3">
        {/* Left Section */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          <button
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="text-theme-text-primary hover:text-theme-accent-primary transition-colors p-2 rounded-lg hover:bg-theme-bg-hover"
            title={sidebarOpen ? 'Close Sidebar' : 'Open Sidebar'}
          >
            {sidebarOpen ? <FaTimes className="w-5 h-5" /> : <FaBars className="w-5 h-5" />}
          </button>

          <div className="flex items-center space-x-3">
            <FaStore className="text-theme-accent-primary text-xl sm:text-2xl" />
            <div className="hidden sm:block">
              <h1 className="text-lg sm:text-xl font-fredoka text-theme-text-primary">
                {user?.shopName || 'Zone Shop'}
              </h1>
              <div className="flex items-center space-x-2 text-xs text-theme-text-tertiary">
                <FaMapMarkerAlt className="w-3 h-3" />
                <span className="font-raleway">Zone {zoneId}</span>
                <span>•</span>
                <FaClock className="w-3 h-3" />
                <span>Open</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Theme Toggle */}
          <ThemeToggle variant="icon-only" showLabel={false} />

          {/* Notifications */}
          <div className="relative">
            <button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative text-theme-text-secondary hover:text-theme-text-primary p-2 rounded-lg hover:bg-theme-bg-hover transition-colors"
              title="Notifications"
            >
              <FaBell className="w-5 h-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-status-error text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {unreadCount}
                </span>
              )}
            </button>

            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute right-0 mt-2 w-80 admin-card rounded-xl shadow-xl backdrop-blur-sm border border-theme-border-primary"
                  style={{
                    zIndex: 99999,
                    position: 'fixed',
                    top: '4rem',
                    right: '1rem'
                  }}
                >
                  <div className="p-4 border-b border-theme-border-primary">
                    <h3 className="text-theme-text-primary font-fredoka text-lg">Notifications</h3>
                  </div>
                  <div className="max-h-96 overflow-y-auto">
                    {notifications.map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-theme-border-primary hover:bg-theme-bg-hover transition-colors ${!notification.read ? 'bg-theme-accent-primary/5' : ''
                          }`}
                      >
                        <div className="flex items-start space-x-3">
                          <notification.icon className={`w-5 h-5 mt-0.5 ${notification.color}`} />
                          <div className="flex-1 min-w-0">
                            <p className="text-theme-text-primary font-raleway text-sm font-medium">
                              {notification.title}
                            </p>
                            <p className="text-theme-text-secondary font-raleway text-xs mt-1">
                              {notification.message}
                            </p>
                            <p className="text-theme-text-tertiary font-raleway text-xs mt-1">
                              {notification.time}
                            </p>
                          </div>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-theme-accent-primary rounded-full mt-2"></div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="p-3 text-center border-t border-theme-border-primary">
                    <button className="text-theme-accent-primary hover:text-theme-accent-hover font-raleway text-sm">
                      View All Notifications
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowProfile(!showProfile)}
              className="flex items-center space-x-2 text-theme-text-secondary hover:text-theme-text-primary p-2 rounded-lg hover:bg-theme-bg-hover transition-colors"
            >
              <div className="w-8 h-8 bg-theme-accent-primary rounded-full flex items-center justify-center">
                <FaUser className="w-4 h-4 text-white" />
              </div>
              <div className="hidden sm:block text-left">
                <p className="text-theme-text-primary font-raleway text-sm">{user?.name}</p>
                <p className="text-theme-text-tertiary text-xs font-raleway">Vendor</p>
              </div>
            </button>

            <AnimatePresence>
              {showProfile && (
                <motion.div
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: 10, scale: 0.95 }}
                  className="absolute right-0 mt-2 w-48 admin-card rounded-xl shadow-xl backdrop-blur-sm border border-theme-border-primary"
                  style={{
                    zIndex: 99999,
                    position: 'fixed',
                    top: '4rem',
                    right: '1rem'
                  }}
                >
                  <div className="p-4 border-b border-theme-border-primary">
                    <p className="text-theme-text-primary font-fredoka text-sm">{user?.name}</p>
                    <p className="text-theme-text-tertiary font-raleway text-xs">{user?.shopName}</p>
                  </div>
                  <div className="p-2">
                    <button
                      onClick={handleProfileClick}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-theme-text-secondary hover:text-theme-text-primary hover:bg-theme-bg-hover rounded-lg transition-colors"
                    >
                      <FaUser className="w-4 h-4" />
                      <span className="font-raleway text-sm">Shop Profile</span>
                    </button>
                    <button
                      onClick={handleSettingsClick}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-theme-text-secondary hover:text-theme-text-primary hover:bg-theme-bg-hover rounded-lg transition-colors"
                    >
                      <FaCog className="w-4 h-4" />
                      <span className="font-raleway text-sm">Settings</span>
                    </button>
                  </div>
                  <div className="p-2 border-t border-theme-border-primary">
                    <button
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-3 px-3 py-2 text-status-error hover:text-status-error hover:bg-status-error-light rounded-lg transition-colors"
                    >
                      <FaSignOutAlt className="w-4 h-4" />
                      <span className="font-raleway text-sm">Logout</span>
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>



      {/* Click outside handlers */}
      {(showNotifications || showProfile) && (
        <div
          className="fixed inset-0 z-[9998]"
          onClick={() => {
            setShowNotifications(false);
            setShowProfile(false);
          }}
        />
      )}
    </motion.nav>
  );
};

export default ZoneShopNavbar;
