import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaSort,
  FaImage,
  FaUtensils,
  FaArrowUp,
  FaArrowDown,
  FaCheckCircle,
  FaTimesCircle
} from 'react-icons/fa';
import ZoneShopLayout from '../ZoneShopLayout';
import ImageUpload from '../../common/ImageUpload';

const MenuCategories = () => {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image: '',
    displayOrder: '',
    active: true,
    color: '#FF6B35'
  });

  // Mock category data
  const mockCategories = [
    {
      id: 1,
      name: 'Pizza',
      description: 'Authentic Italian pizzas with fresh ingredients',
      image: 'https://images.unsplash.com/photo-1513104890138-7c749659a591?w=300',
      displayOrder: 1,
      active: true,
      color: '#FF6B35',
      itemCount: 8,
      totalSales: 2450.75
    },
    {
      id: 2,
      name: 'Sides',
      description: 'Delicious appetizers and side dishes',
      image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=300',
      displayOrder: 2,
      active: true,
      color: '#4ECDC4',
      itemCount: 5,
      totalSales: 890.50
    },
    {
      id: 3,
      name: 'Beverages',
      description: 'Refreshing drinks and beverages',
      image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=300',
      displayOrder: 3,
      active: true,
      color: '#45B7D1',
      itemCount: 6,
      totalSales: 650.25
    },
    {
      id: 4,
      name: 'Desserts',
      description: 'Sweet treats and desserts',
      image: 'https://images.unsplash.com/photo-1551024506-0bccd828d307?w=300',
      displayOrder: 4,
      active: false,
      color: '#96CEB4',
      itemCount: 3,
      totalSales: 320.00
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setCategories(mockCategories);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedCategories = [...filteredCategories].sort((a, b) => a.displayOrder - b.displayOrder);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (editingCategory) {
      setCategories(categories.map(category =>
        category.id === editingCategory.id
          ? {
            ...category,
            ...formData,
            displayOrder: parseInt(formData.displayOrder)
          }
          : category
      ));
    } else {
      const newCategory = {
        ...formData,
        id: Date.now(),
        displayOrder: parseInt(formData.displayOrder) || categories.length + 1,
        itemCount: 0,
        totalSales: 0
      };
      setCategories([...categories, newCategory]);
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      image: '',
      displayOrder: '',
      active: true,
      color: '#FF6B35'
    });
    setEditingCategory(null);
    setShowForm(false);
  };

  const handleEdit = (category) => {
    setFormData({
      name: category.name,
      description: category.description,
      image: category.image,
      displayOrder: category.displayOrder.toString(),
      active: category.active,
      color: category.color
    });
    setEditingCategory(category);
    setShowForm(true);
  };

  const handleDelete = (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category?')) {
      setCategories(categories.filter(category => category.id !== categoryId));
    }
  };

  const toggleActive = (categoryId) => {
    setCategories(categories.map(category =>
      category.id === categoryId
        ? { ...category, active: !category.active }
        : category
    ));
  };

  const moveCategory = (categoryId, direction) => {
    const categoryIndex = categories.findIndex(c => c.id === categoryId);
    if (categoryIndex === -1) return;

    const newCategories = [...categories];
    const targetIndex = direction === 'up' ? categoryIndex - 1 : categoryIndex + 1;

    if (targetIndex >= 0 && targetIndex < categories.length) {
      // Swap display orders
      const temp = newCategories[categoryIndex].displayOrder;
      newCategories[categoryIndex].displayOrder = newCategories[targetIndex].displayOrder;
      newCategories[targetIndex].displayOrder = temp;

      setCategories(newCategories);
    }
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-primary text-xl">Loading categories...</div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Menu Categories</h1>
            <p className="text-primary/70 font-raleway">Organize your menu items into categories</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2"
          >
            <FaPlus />
            <span>Add Category</span>
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div className="bg-primary/10 backdrop-blur-lg rounded-xl p-4 border border-primary/10">
            <h3 className="text-2xl font-fredoka text-primary">{categories.length}</h3>
            <p className="text-primary/70 font-raleway text-sm">Total Categories</p>
          </div>
          <div className="bg-primary/10 backdrop-blur-lg rounded-xl p-4 border border-primary/10">
            <h3 className="text-2xl font-fredoka text-green-400">{categories.filter(c => c.active).length}</h3>
            <p className="text-primary/70 font-raleway text-sm">Active</p>
          </div>
          <div className="bg-primary/10 backdrop-blur-lg rounded-xl p-4 border border-primary/10">
            <h3 className="text-2xl font-fredoka text-primary">{categories.reduce((sum, c) => sum + c.itemCount, 0)}</h3>
            <p className="text-primary/70 font-raleway text-sm">Total Items</p>
          </div>
          <div className="bg-primary/10 backdrop-blur-lg rounded-xl p-4 border border-primary/10">
            <h3 className="text-2xl font-fredoka text-accent">${categories.reduce((sum, c) => sum + c.totalSales, 0).toFixed(2)}</h3>
            <p className="text-primary/70 font-raleway text-sm">Total Sales</p>
          </div>
        </div>

        {/* Search */}
        <div className="bg-primary/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary/10">
          <div className="relative">
            <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary/50" />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-primary/10 border border-primary/20 rounded-lg pl-10 pr-4 py-2 text-primary placeholder-primary/50 focus:outline-none focus:border-accent"
            />
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedCategories.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`bg-primary/10 backdrop-blur-lg rounded-2xl overflow-hidden border border-primary/10 ${!category.active ? 'opacity-60' : ''
                }`}
            >
              {/* Category Image */}
              <div className="relative h-48 bg-primary/5">
                {category.image ? (
                  <img src={category.image} alt={category.name} className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <FaUtensils className="text-primary/40 text-4xl" />
                  </div>
                )}
                <div
                  className="absolute top-3 left-3 w-4 h-4 rounded-full border-2 border-primary"
                  style={{ backgroundColor: category.color }}
                />
                <div className="absolute top-3 right-3 flex space-x-2">
                  <span className="bg-transparent text-primary px-2 py-1 rounded-full text-xs font-raleway">
                    #{category.displayOrder}
                  </span>
                  <button
                    onClick={() => toggleActive(category.id)}
                    className={`p-2 rounded-full ${category.active
                      ? 'bg-green-500 text-primary'
                      : 'bg-red-500 text-primary'
                      }`}
                  >
                    {category.active ? <FaCheckCircle /> : <FaTimesCircle />}
                  </button>
                </div>
              </div>

              {/* Category Details */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="text-primary font-fredoka text-lg">{category.name}</h3>
                    <p className="text-primary/60 font-raleway text-sm">{category.itemCount} items</p>
                  </div>
                  <div className="flex flex-col space-y-1">
                    <button
                      onClick={() => moveCategory(category.id, 'up')}
                      disabled={index === 0}
                      className="p-1 text-primary/60 hover:text-primary disabled:opacity-30 disabled:cursor-not-allowed"
                    >
                      <FaArrowUp />
                    </button>
                    <button
                      onClick={() => moveCategory(category.id, 'down')}
                      disabled={index === sortedCategories.length - 1}
                      className="p-1 text-primary/60 hover:text-primary disabled:opacity-30 disabled:cursor-not-allowed"
                    >
                      <FaArrowDown />
                    </button>
                  </div>
                </div>

                <p className="text-primary/70 font-raleway text-sm mb-3 line-clamp-2">{category.description}</p>

                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm text-primary/60">
                    <p>Sales: ${category.totalSales.toFixed(2)}</p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(category)}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded-lg font-raleway text-sm flex items-center justify-center space-x-2"
                  >
                    <FaEdit />
                    <span>Edit</span>
                  </button>
                  <button className="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-3 rounded-lg font-raleway text-sm flex items-center justify-center space-x-2">
                    <FaEye />
                    <span>View Items</span>
                  </button>
                  <button
                    onClick={() => handleDelete(category.id)}
                    className="bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded-lg"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Add/Edit Category Modal */}
        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-transparent backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => e.target === e.currentTarget && resetForm()}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-transparent backdrop-blur-xl border border-primary/20 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              >
                <h2 className="text-2xl font-fredoka text-primary mb-6">
                  {editingCategory ? 'Edit Category' : 'Add New Category'}
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Image Upload */}
                  <ImageUpload
                    currentImage={formData.image}
                    onImageChange={(imageUrl) => setFormData({ ...formData, image: imageUrl })}
                    label="Category Image"
                    size="large"
                    shape="rounded"
                  />

                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Category Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full bg-primary/10 border border-primary/20 rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Display Order</label>
                      <input
                        type="number"
                        min="1"
                        value={formData.displayOrder}
                        onChange={(e) => setFormData({ ...formData, displayOrder: e.target.value })}
                        className="w-full bg-primary/10 border border-primary/20 rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        placeholder="1"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full bg-primary/10 border border-primary/20 rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                      rows="3"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Category Color</label>
                    <div className="flex items-center space-x-4">
                      <input
                        type="color"
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="w-16 h-10 bg-primary/10 border border-primary/20 rounded-lg cursor-pointer"
                      />
                      <input
                        type="text"
                        value={formData.color}
                        onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                        className="flex-1 bg-primary/10 border border-primary/20 rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        placeholder="#FF6B35"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="active"
                      checked={formData.active}
                      onChange={(e) => setFormData({ ...formData, active: e.target.checked })}
                      className="w-4 h-4 text-accent bg-primary/10 border-primary/20 rounded focus:ring-accent"
                    />
                    <label htmlFor="active" className="text-primary font-raleway">Active Category</label>
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={resetForm}
                      className="px-6 py-2 bg-primary/10 hover:bg-primary/20 text-primary rounded-lg font-raleway transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-accent hover:bg-accent/90 text-primary rounded-lg font-raleway transition-colors"
                    >
                      {editingCategory ? 'Update Category' : 'Add Category'}
                    </button>
                  </div>
                </form>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ZoneShopLayout>
  );
};

export default MenuCategories;
