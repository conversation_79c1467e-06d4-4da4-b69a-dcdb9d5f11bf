import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaBoxes,
  FaExclamationTriangle,
  FaCheckCircle,
  FaTimesCircle,
  FaEdit,
  FaSearch,
  FaFilter,
  FaPlus,
  FaMinus
} from 'react-icons/fa';
import ZoneShopLayout from '../ZoneShopLayout';

const ZoneShopInventory = () => {
  const { zoneId, shopId } = useParams();
  const [inventoryItems, setInventoryItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [loading, setLoading] = useState(true);

  // Mock inventory data
  const mockInventoryItems = [
    {
      id: 1,
      name: 'Margherita Pizza',
      category: 'Pizza',
      currentStock: 25,
      minStock: 10,
      maxStock: 50,
      unit: 'portions',
      status: 'in_stock',
      lastUpdated: '2024-01-15T10:30:00Z',
      price: 299
    },
    {
      id: 2,
      name: 'Cheese',
      category: 'Ingredients',
      currentStock: 5,
      minStock: 10,
      maxStock: 30,
      unit: 'kg',
      status: 'low_stock',
      lastUpdated: '2024-01-15T09:15:00Z',
      price: 450
    },
    {
      id: 3,
      name: 'Pepperoni Pizza',
      category: 'Pizza',
      currentStock: 0,
      minStock: 8,
      maxStock: 40,
      unit: 'portions',
      status: 'out_of_stock',
      lastUpdated: '2024-01-15T08:45:00Z',
      price: 349
    },
    {
      id: 4,
      name: 'Tomato Sauce',
      category: 'Ingredients',
      currentStock: 15,
      minStock: 5,
      maxStock: 25,
      unit: 'liters',
      status: 'in_stock',
      lastUpdated: '2024-01-15T11:00:00Z',
      price: 120
    },
    {
      id: 5,
      name: 'Garlic Bread',
      category: 'Sides',
      currentStock: 3,
      minStock: 8,
      maxStock: 20,
      unit: 'portions',
      status: 'low_stock',
      lastUpdated: '2024-01-15T10:45:00Z',
      price: 89
    }
  ];

  useEffect(() => {
    // Simulate loading inventory data
    setTimeout(() => {
      setInventoryItems(mockInventoryItems);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status) => {
    switch (status) {
      case 'in_stock':
        return <FaCheckCircle className="text-green-500" />;
      case 'low_stock':
        return <FaExclamationTriangle className="text-yellow-500" />;
      case 'out_of_stock':
        return <FaTimesCircle className="text-red-500" />;
      default:
        return <FaBoxes className="text-theme-text-tertiary" />;
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'in_stock':
        return 'In Stock';
      case 'low_stock':
        return 'Low Stock';
      case 'out_of_stock':
        return 'Out of Stock';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'in_stock':
        return 'text-green-600 bg-green-100';
      case 'low_stock':
        return 'text-yellow-600 bg-yellow-100';
      case 'out_of_stock':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  const updateStock = (itemId, change) => {
    setInventoryItems(prev => prev.map(item => {
      if (item.id === itemId) {
        const newStock = Math.max(0, item.currentStock + change);
        let newStatus = 'in_stock';
        
        if (newStock === 0) {
          newStatus = 'out_of_stock';
        } else if (newStock <= item.minStock) {
          newStatus = 'low_stock';
        }

        return {
          ...item,
          currentStock: newStock,
          status: newStatus,
          lastUpdated: new Date().toISOString()
        };
      }
      return item;
    }));
  };

  const filteredItems = inventoryItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.category.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || item.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const stats = {
    total: inventoryItems.length,
    inStock: inventoryItems.filter(item => item.status === 'in_stock').length,
    lowStock: inventoryItems.filter(item => item.status === 'low_stock').length,
    outOfStock: inventoryItems.filter(item => item.status === 'out_of_stock').length
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-theme-accent-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-secondary">Loading inventory...</p>
          </div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">
              Inventory Management
            </h1>
            <p className="text-theme-text-secondary font-raleway">
              Track and manage your menu item availability
            </p>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center space-x-3">
              <FaBoxes className="text-theme-accent-primary text-xl" />
              <div>
                <p className="text-2xl font-fredoka text-theme-text-primary">{stats.total}</p>
                <p className="text-theme-text-secondary font-raleway text-sm">Total Items</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center space-x-3">
              <FaCheckCircle className="text-green-500 text-xl" />
              <div>
                <p className="text-2xl font-fredoka text-theme-text-primary">{stats.inStock}</p>
                <p className="text-theme-text-secondary font-raleway text-sm">In Stock</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center space-x-3">
              <FaExclamationTriangle className="text-yellow-500 text-xl" />
              <div>
                <p className="text-2xl font-fredoka text-theme-text-primary">{stats.lowStock}</p>
                <p className="text-theme-text-secondary font-raleway text-sm">Low Stock</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center space-x-3">
              <FaTimesCircle className="text-red-500 text-xl" />
              <div>
                <p className="text-2xl font-fredoka text-theme-text-primary">{stats.outOfStock}</p>
                <p className="text-theme-text-secondary font-raleway text-sm">Out of Stock</p>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Filters */}
        <div className="admin-card p-6 rounded-xl">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-tertiary" />
                <input
                  type="text"
                  placeholder="Search items..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-theme-border-primary rounded-lg bg-theme-bg-secondary text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-accent-primary"
                />
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FaFilter className="text-theme-text-tertiary" />
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 border border-theme-border-primary rounded-lg bg-theme-bg-secondary text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-accent-primary"
              >
                <option value="all">All Status</option>
                <option value="in_stock">In Stock</option>
                <option value="low_stock">Low Stock</option>
                <option value="out_of_stock">Out of Stock</option>
              </select>
            </div>
          </div>
        </div>

        {/* Inventory Table */}
        <div className="admin-card rounded-xl overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-theme-bg-secondary">
                <tr>
                  <th className="text-left p-4 font-raleway font-medium text-theme-text-primary">Item</th>
                  <th className="text-left p-4 font-raleway font-medium text-theme-text-primary">Category</th>
                  <th className="text-left p-4 font-raleway font-medium text-theme-text-primary">Stock</th>
                  <th className="text-left p-4 font-raleway font-medium text-theme-text-primary">Status</th>
                  <th className="text-left p-4 font-raleway font-medium text-theme-text-primary">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredItems.map((item, index) => (
                  <motion.tr
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="border-b border-theme-border-primary hover:bg-theme-bg-hover"
                  >
                    <td className="p-4">
                      <div>
                        <p className="font-raleway font-medium text-theme-text-primary">{item.name}</p>
                        <p className="text-sm text-theme-text-tertiary">₹{item.price}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className="text-theme-text-secondary font-raleway">{item.category}</span>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="font-raleway text-theme-text-primary">
                          {item.currentStock} {item.unit}
                        </p>
                        <p className="text-xs text-theme-text-tertiary">
                          Min: {item.minStock} | Max: {item.maxStock}
                        </p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(item.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-raleway ${getStatusColor(item.status)}`}>
                          {getStatusText(item.status)}
                        </span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => updateStock(item.id, -1)}
                          disabled={item.currentStock === 0}
                          className="p-2 text-red-500 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                          title="Decrease stock"
                        >
                          <FaMinus />
                        </button>
                        <button
                          onClick={() => updateStock(item.id, 1)}
                          className="p-2 text-green-500 hover:bg-green-50 rounded-lg transition-colors"
                          title="Increase stock"
                        >
                          <FaPlus />
                        </button>
                        <button
                          className="p-2 text-theme-text-secondary hover:text-theme-accent-primary hover:bg-theme-bg-hover rounded-lg transition-colors"
                          title="Edit item"
                        >
                          <FaEdit />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <FaBoxes className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
            <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">No items found</h3>
            <p className="text-theme-text-secondary font-raleway">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Start by adding items to your inventory'
              }
            </p>
          </div>
        )}
      </div>
    </ZoneShopLayout>
  );
};

export default ZoneShopInventory;
