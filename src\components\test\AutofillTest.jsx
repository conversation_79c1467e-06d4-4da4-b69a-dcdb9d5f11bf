import React from 'react';

const AutofillTest = () => {
  return (
    <div className="min-h-screen admin-layout p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-fredoka text-theme-text-primary mb-8">Autofill Protection Test</h1>
        
        {/* Regular inputs without protection */}
        <div className="admin-card p-6 rounded-xl">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Regular Inputs (No Protection)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Name</label>
              <input
                type="text"
                placeholder="Enter your name"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Email</label>
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Phone</label>
              <input
                type="tel"
                placeholder="Enter your phone"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Address</label>
              <input
                type="text"
                placeholder="Enter your address"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Protected inputs */}
        <div className="admin-card p-6 rounded-xl">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Protected Inputs (With autofill-protected class)</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Name</label>
              <input
                type="text"
                placeholder="Enter your name"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors autofill-protected"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Email</label>
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors autofill-protected"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Phone</label>
              <input
                type="tel"
                placeholder="Enter your phone"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors autofill-protected"
              />
            </div>
            <div>
              <label className="block text-theme-text-primary font-raleway mb-2">Address</label>
              <input
                type="text"
                placeholder="Enter your address"
                className="w-full bg-theme-bg-secondary border border-theme-border-primary rounded-lg px-4 py-3 text-theme-text-primary focus:outline-none focus:border-theme-accent-primary transition-colors autofill-protected"
              />
            </div>
          </div>
        </div>

        {/* Login form style */}
        <div className="admin-card p-6 rounded-xl login-form">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Login Form Style</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white font-raleway mb-2">Username</label>
              <input
                type="text"
                placeholder="Enter username"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-accent transition-colors"
              />
            </div>
            <div>
              <label className="block text-white font-raleway mb-2">Password</label>
              <input
                type="password"
                placeholder="Enter password"
                className="w-full bg-white/10 border border-white/20 rounded-lg px-4 py-3 text-white focus:outline-none focus:border-accent transition-colors"
              />
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="admin-card p-6 rounded-xl">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">Testing Instructions</h2>
          <div className="space-y-3 text-theme-text-secondary">
            <p>1. <strong>Try browser autofill:</strong> Click on any input field and use your browser's autofill feature (if you have saved data).</p>
            <p>2. <strong>Compare sections:</strong> Notice how the "Protected Inputs" maintain theme colors even with autofill, while "Regular Inputs" might show browser default colors.</p>
            <p>3. <strong>Password managers:</strong> If you use a password manager (1Password, LastPass, etc.), try using it on these fields.</p>
            <p>4. <strong>Theme switching:</strong> Switch between light and dark themes to see how autofill adapts.</p>
            <p>5. <strong>Focus states:</strong> Notice how focus states are preserved even with autofill active.</p>
          </div>
        </div>

        {/* CSS Classes Reference */}
        <div className="admin-card p-6 rounded-xl">
          <h2 className="text-xl font-fredoka text-theme-text-primary mb-4">CSS Classes Reference</h2>
          <div className="space-y-3 text-theme-text-secondary font-mono text-sm">
            <p><code className="bg-theme-bg-hover px-2 py-1 rounded">autofill-protected</code> - Comprehensive autofill protection</p>
            <p><code className="bg-theme-bg-hover px-2 py-1 rounded">login-form</code> - Special styling for login forms</p>
            <p><code className="bg-theme-bg-hover px-2 py-1 rounded">search-input</code> - Autofill styling for search inputs</p>
            <p><code className="bg-theme-bg-hover px-2 py-1 rounded">admin-card</code> - Automatic autofill protection for admin forms</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutofillTest;
