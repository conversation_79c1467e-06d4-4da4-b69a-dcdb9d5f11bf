@import url('https://fonts.googleapis.com/css2?family=Fredoka+One&family=Raleway:wght@400;600&display=swap');
@import './styles/themes.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --navbar-height: 6rem;
    }

    body {
        @apply bg-primary-bg font-raleway;
    }

    /* Comprehensive autofill styling for all input types */
    input:-webkit-autofill,
    input:-webkit-autofill:hover,
    input:-webkit-autofill:focus,
    input:-webkit-autofill:active,
    textarea:-webkit-autofill,
    textarea:-webkit-autofill:hover,
    textarea:-webkit-autofill:focus,
    textarea:-webkit-autofill:active,
    select:-webkit-autofill,
    select:-webkit-autofill:hover,
    select:-webkit-autofill:focus,
    select:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-secondary) !important;
        border-color: var(--border-primary) !important;
        transition: background-color 5000s ease-in-out 0s !important;
        caret-color: var(--text-primary) !important;
    }

    /* For Firefox autofill */
    input:-moz-autofill,
    textarea:-moz-autofill,
    select:-moz-autofill {
        background-color: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;
        -moz-box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
    }

    /* For Edge/IE autofill */
    input:-ms-input-placeholder,
    textarea:-ms-input-placeholder {
        color: var(--text-tertiary) !important;
    }

    /* Additional autofill states for password managers */
    input[data-com-onepassword-filled],
    input[data-dashlane-rid],
    input[data-lastpass-icon-root],
    input[data-bitwarden-watching] {
        background-color: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;
    }

    /* Specific autofill styling for admin forms */
    .admin-card input:-webkit-autofill,
    .admin-card textarea:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-secondary) !important;
        border-color: var(--border-primary) !important;
    }

    /* Autofill for theme-aware inputs */
    input.bg-theme-bg-secondary:-webkit-autofill,
    textarea.bg-theme-bg-secondary:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 30px var(--bg-secondary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-secondary) !important;
        border-color: var(--border-primary) !important;
    }

    /* Ensure autofill doesn't override focus states */
    input:-webkit-autofill:focus,
    textarea:-webkit-autofill:focus {
        border-color: var(--accent-primary) !important;
        outline: none !important;
        box-shadow: 0 0 0 2px var(--accent-primary) !important;
    }

    /* Remove autofill icons that might interfere with design */
    input::-webkit-contacts-auto-fill-button,
    input::-webkit-credentials-auto-fill-button {
        visibility: hidden;
        display: none !important;
        pointer-events: none;
        height: 0;
        width: 0;
        margin: 0;
    }

    /* Light theme specific autofill styling */
    .light input:-webkit-autofill,
    .light textarea:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 30px #ffffff inset !important;
        -webkit-text-fill-color: #0f172a !important;
        background-color: #ffffff !important;
        border-color: #e2e8f0 !important;
    }

    .light input:-webkit-autofill:focus,
    .light textarea:-webkit-autofill:focus {
        border-color: #FF6B00 !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.2) !important;
    }

    /* Dark theme specific autofill styling */
    .dark input:-webkit-autofill,
    .dark textarea:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 30px #121212 inset !important;
        -webkit-text-fill-color: #ffffff !important;
        background-color: #121212 !important;
        border-color: #2a2a2a !important;
    }

    .dark input:-webkit-autofill:focus,
    .dark textarea:-webkit-autofill:focus {
        border-color: #FF6B00 !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.3) !important;
    }

    /* Utility class for inputs that need extra autofill protection */
    .autofill-protected:-webkit-autofill,
    .autofill-protected:-webkit-autofill:hover,
    .autofill-protected:-webkit-autofill:focus,
    .autofill-protected:-webkit-autofill:active {
        -webkit-box-shadow: 0 0 0 1000px var(--bg-secondary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-secondary) !important;
        border-color: var(--border-primary) !important;
        transition: background-color 5000s ease-in-out 0s !important;
        caret-color: var(--text-primary) !important;
    }

    /* Force override for stubborn browsers */
    .autofill-protected {
        background-color: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
        border-color: var(--border-primary) !important;
    }

    .autofill-protected:focus {
        border-color: var(--accent-primary) !important;
        outline: none !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.2) !important;
    }

    /* Login form specific autofill styling */
    .login-form input:-webkit-autofill,
    .login-form input:-webkit-autofill:hover,
    .login-form input:-webkit-autofill:focus {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        background-color: rgba(255, 255, 255, 0.1) !important;
        border-color: rgba(255, 255, 255, 0.2) !important;
        transition: background-color 5000s ease-in-out 0s !important;
    }

    /* Search input autofill styling */
    .search-input:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px var(--bg-primary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-primary) !important;
        border-color: var(--border-primary) !important;
    }

    /* Prevent autofill from changing placeholder color */
    input:-webkit-autofill::placeholder,
    textarea:-webkit-autofill::placeholder {
        color: var(--text-tertiary) !important;
        opacity: 1 !important;
    }

    /* Ensure autofill works with custom styled inputs */
    input[class*="bg-theme"]:-webkit-autofill,
    textarea[class*="bg-theme"]:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px var(--bg-secondary) inset !important;
        -webkit-text-fill-color: var(--text-primary) !important;
        background-color: var(--bg-secondary) !important;
        border-color: var(--border-primary) !important;
    }

    /* For other browsers */
    input:autofill {
        background-color: rgba(255, 255, 255, 0.1) !important;
        color: white !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Additional autofill fixes for login forms */
    input[type="text"]:-webkit-autofill,

    /* Signup form specific autofill: force white background and dark text */
    .signup-form input:-webkit-autofill,
    .signup-form input:-webkit-autofill:hover,
    .signup-form input:-webkit-autofill:focus,
    .signup-form textarea:-webkit-autofill,
    .signup-form select:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
        -webkit-text-fill-color: #0f172a !important;
        /* slate-900 */
        background-color: #ffffff !important;
        border-color: #e2e8f0 !important;
        /* slate-200 */
        caret-color: #0f172a !important;
    }

    /* Ensure focus ring is accent */
    .signup-form input:-webkit-autofill:focus,
    .signup-form textarea:-webkit-autofill:focus {
        border-color: var(--accent-primary) !important;
        box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.2) !important;
    }

    input[type="password"]:-webkit-autofill,
    input[type="tel"]:-webkit-autofill,
    input[type="email"]:-webkit-autofill {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        caret-color: white !important;
    }

    /* Ensure placeholder text remains visible */
    input:-webkit-autofill::placeholder {
        color: rgba(255, 255, 255, 0.6) !important;
        opacity: 1 !important;
    }

    /* Force consistent styling for all autofill states */
    input:-webkit-autofill:focus {
        -webkit-box-shadow: 0 0 0 1000px rgba(255, 255, 255, 0.1) inset !important;
        -webkit-text-fill-color: white !important;
        border: 1px solid #FF6B35 !important;
        /* accent color */
    }

    /* Ensure icons remain visible over autofill background */
    .relative input:-webkit-autofill+* {
        z-index: 10;
        position: relative;
    }

    /* Hide browser's default password reveal button */
    input[type="password"]::-ms-reveal,
    input[type="password"]::-ms-clear {
        display: none;
    }

    input[type="password"]::-webkit-credentials-auto-fill-button,
    input[type="password"]::-webkit-strong-password-auto-fill-button {
        display: none !important;
    }

    /* Hide Edge's password reveal button */
    input[type="password"]::-ms-reveal {
        display: none;
    }

    /* Hide Chrome's password reveal button */
    input[type="password"]::-webkit-reveal {
        display: none;
    }

    /* Additional browser-specific password reveal button hiding */
    input[type="password"]::-webkit-textfield-decoration-container {
        visibility: hidden;
    }

    /* Ensure our custom eye button has proper z-index */
    .relative button[type="button"] {
        z-index: 10;
        position: relative;
    }

    /* Ensure password field icons are properly positioned */
    .relative .absolute {
        z-index: 10;
    }

    /* Prevent browser password reveal button from interfering */
    input[type="password"]::-webkit-outer-spin-button,
    input[type="password"]::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Password field icon positioning - ensure proper containment */
    .relative div.absolute.inset-y-0 {
        display: flex !important;
        align-items: center !important;
        pointer-events: none !important;
        z-index: 10 !important;
    }

    .relative div.absolute.inset-y-0.right-0 {
        pointer-events: auto !important;
        z-index: 20 !important;
    }

    .relative div.absolute.inset-y-0 button {
        pointer-events: auto !important;
        z-index: 21 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    /* Ensure input field has proper stacking */
    .relative input[type="password"],
    .relative input[type="text"] {
        position: relative !important;
        z-index: 1 !important;
    }

    /* Prevent any layout issues */
    .relative {
        position: relative !important;
        isolation: isolate !important;
    }

    /* Line clamp utility for text truncation */
    .line-clamp-2 {
        display: -webkit-box;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-1 {
        display: -webkit-box;
        line-clamp: 1;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Hide scrollbar for horizontal scroll sections */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Force proper stacking context for password fields */
    .relative {
        isolation: isolate;
    }

    /* Prevent any browser extensions from interfering */
    input[type="password"] {
        position: relative;
        z-index: 1;
    }
}

/* ===== Signup-only autofill hard overrides (must be last) ===== */
.signup-form .autofill-protected,
.signup-form input,
.signup-form textarea,
.signup-form select {
    background-color: #ffffff !important;
    color: #0f172a !important;
    /* slate-900 */
    border-color: #e2e8f0 !important;
    /* slate-200 */
}

.signup-form .autofill-protected:-webkit-autofill,
.signup-form input:-webkit-autofill,
.signup-form input:-webkit-autofill:hover,
.signup-form input:-webkit-autofill:focus,
.signup-form textarea:-webkit-autofill,
.signup-form select:-webkit-autofill,
.signup-form input[type="password"]:-webkit-autofill,
.signup-form input[type="email"]:-webkit-autofill,
.signup-form input[type="tel"]:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 1000px #ffffff inset !important;
    -webkit-text-fill-color: #0f172a !important;
    background-color: #ffffff !important;
    border-color: #e2e8f0 !important;
    caret-color: #0f172a !important;
}

.signup-form input:-webkit-autofill:focus,
.signup-form textarea:-webkit-autofill:focus {
    border-color: var(--accent-primary) !important;
    box-shadow: 0 0 0 2px rgba(255, 107, 0, 0.2) !important;
}