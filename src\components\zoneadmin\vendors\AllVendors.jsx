import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams } from 'react-router-dom';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaStore,
  FaSearch,
  FaFilter,
  FaTimes,
  FaDownload,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaMapMarkerAlt,
  FaCheckCircle,
  FaTimesCircle,
  FaClock,
  FaRupeeSign,
  FaUtensils,
  FaStar,
  FaChartLine,
  FaUserPlus,
  FaCheck
} from 'react-icons/fa';
import ZoneAdminLayout from '../ZoneAdminLayout';
import ImageUpload from '../../common/ImageUpload';
import LocalStorageService from '../../../services/LocalStorageService';
import QuotaBanner from '../common/QuotaBanner';


const AllVendors = () => {
  const [vendors, setVendors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingVendor, setEditingVendor] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    cuisine: '',
    ownerName: '',
    ownerPhone: '',
    ownerEmail: '',
    status: 'active',
    logo: null,
    coverImage: null
  });

  // Get zone ID from URL params
  const { zoneId } = useParams();

  useEffect(() => {
    const loadVendors = () => {
      setLoading(true);
      try {
        const zoneVendors = LocalStorageService.getZoneVendors(zoneId);
        setVendors(zoneVendors);
      } catch (error) {
        console.error('Error loading vendors:', error);
        setVendors([]);
      } finally {
        setLoading(false);
      }
    };

    if (zoneId) {
      loadVendors();
    }
  }, [zoneId]);

  const filteredVendors = vendors.filter(vendor => {
    const matchesSearch = vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.cuisine.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vendor.ownerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || vendor.status === statusFilter;
    return matchesSearch && matchesStatus;
  });



  const handleSubmit = (e) => {
    e.preventDefault();

    if (editingVendor) {
      // Update existing vendor
      const updatedVendor = LocalStorageService.updateZoneVendor(zoneId, editingVendor.id, formData);
      if (updatedVendor) {
        setVendors(vendors.map(vendor =>
          vendor.id === editingVendor.id ? updatedVendor : vendor
        ));
      }
    } else {
      // Enforce plan limit before adding
      try {
        const sub = JSON.parse(localStorage.getItem('tableserve_subscription'));
        const max = sub?.maxVendors;
        if (max != null && vendors.length >= max) {
          alert(`You have reached your plan limit of ${max} vendors. Upgrade to add more.`);
          return;
        }
      } catch { }

      // Add new vendor
      const newVendor = LocalStorageService.addZoneVendor(zoneId, formData);
      if (newVendor) {
        setVendors([newVendor, ...vendors]);
      }
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      cuisine: '',
      ownerName: '',
      ownerPhone: '',
      ownerEmail: '',
      status: 'active',
      logo: null,
      coverImage: null
    });
    setEditingVendor(null);
    setShowForm(false);
  };

  const handleEdit = (vendor) => {
    setFormData({
      name: vendor.name,
      description: vendor.description,
      cuisine: vendor.cuisine,
      ownerName: vendor.ownerName,
      ownerPhone: vendor.ownerPhone,
      ownerEmail: vendor.ownerEmail,
      status: vendor.status,
      logo: vendor.logo || null,
      coverImage: vendor.coverImage || null
    });
    setEditingVendor(vendor);
    setShowForm(true);
  };

  const handleDelete = (vendorId) => {
    if (window.confirm('Are you sure you want to remove this vendor from your zone?')) {
      const success = LocalStorageService.deleteZoneVendor(zoneId, vendorId);
      if (success) {
        setVendors(vendors.filter(vendor => vendor.id !== vendorId));
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'status-success bg-status-success-light';
      case 'pending': return 'status-warning bg-status-warning-light';
      case 'inactive': return 'status-error bg-status-error-light';
      default: return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  if (loading) {
    return (
      <ZoneAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-primary text-xl">Loading vendors...</div>
        </div>
      </ZoneAdminLayout>
    );
  }

  return (
    <ZoneAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">All Vendors</h1>

            {/* Quota Indicator */}
            <motion.div
              initial={{ opacity: 0, y: -6 }}
              animate={{ opacity: 1, y: 0 }}
              className="rounded-xl "
            >
              <QuotaBanner zoneId={zoneId} currentCount={vendors.length} type="vendors" />
            </motion.div>

            <p className="text-theme-text-secondary mt-6 font-raleway text-sm sm:text-base">Manage all vendors in your food zone</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto bg-theme-accent-primary hover:bg-theme-accent-hover text-theme-text-inverse px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2 disabled:opacity-50"
            disabled={(() => { try { const sub = JSON.parse(localStorage.getItem('tableserve_subscription')); const max = sub?.maxVendors; return max != null && vendors.length >= max; } catch { return false; } })()}
          >
            <FaUserPlus />
            <span>{(() => { try { const sub = JSON.parse(localStorage.getItem('tableserve_subscription')); const max = sub?.maxVendors; if (max != null && vendors.length >= max) return 'Upgrade to add more'; } catch { } return 'Add Vendor'; })()}</span>
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg admin-card rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-theme-accent-primary rounded-xl flex items-center justify-center">
                <FaStore className="text-theme-text-inverse text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-theme-text-primary mb-1">{vendors.length}</h3>
            <p className="text-theme-text-secondary font-raleway">Total Vendors</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/10 backdrop-blur-lg admin-card rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaCheckCircle className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{vendors.filter(v => v.status === 'active').length}</h3>
            <p className="text-secondary font-raleway">Active Vendors</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-lg admin-card rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FaUtensils className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{vendors.reduce((sum, v) => sum + v.menuItems, 0)}</h3>
            <p className="text-secondary font-raleway">Menu Items</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 backdrop-blur-lg admin-card rounded-2xl p-4 sm:p-6 border border-white/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-theme-accent-primary rounded-xl flex items-center justify-center">
                <FaRupeeSign className="text-theme-text-inverse text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">₹{vendors.reduce((sum, v) => sum + v.monthlyRevenue, 0).toLocaleString()}</h3>
            <p className="text-secondary font-raleway">Monthly Revenue</p>
          </motion.div>
        </div>

        {/* Search and Filters */}
        <div className="bg-transparent backdrop-blur-lg rounded-2xl p-4 sm:p-6 border admin-card border-primary">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
              <input
                type="text"
                placeholder="Search vendors, cuisine, or owners..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-transparent border border-primary rounded-lg pl-10 pr-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-transparent border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
            >
              <option value="all" className="text-black bg-transparent">All Status</option>
              <option value="active" className='text-green-500'>Active</option>
              <option value="pending" className='text-yellow-500'>Pending</option>
              <option value="inactive" className='text-red-500'>Inactive</option>
            </select>

          </div>
        </div>

        {/* Vendors Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {filteredVendors.map((vendor) => (
            <motion.div
              key={vendor.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="admin-card rounded-2xl p-6"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-theme-accent-primary rounded-xl flex items-center justify-center overflow-hidden">
                    {vendor.logo ? (
                      <img
                        src={vendor.logo}
                        alt={`${vendor.name} logo`}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <FaStore className="text-theme-text-inverse text-xl" />
                    )}
                  </div>
                  <div>
                    <h3 className="text-theme-text-primary font-fredoka text-lg">{vendor.name}</h3>
                    <p className="text-theme-text-secondary font-raleway text-sm">{vendor.cuisine}</p>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(vendor.status)}`}>
                  {vendor.status.charAt(0).toUpperCase() + vendor.status.slice(1)}
                </span>
              </div>

              <p className="text-theme-text-secondary font-raleway text-sm mb-4">{vendor.description}</p>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-lg font-fredoka text-theme-text-primary">{vendor.totalOrders}</p>
                  <p className="text-theme-text-tertiary font-raleway text-xs">Orders</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1">
                    <FaStar className="text-yellow-400 text-sm" />
                    <p className="text-lg font-fredoka text-theme-text-primary">{vendor.rating}</p>
                  </div>
                  <p className="text-theme-text-tertiary font-raleway text-xs">Rating</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-fredoka text-theme-text-primary">{vendor.menuItems}</p>
                  <p className="text-theme-text-tertiary font-raleway text-xs">Items</p>
                </div>
              </div>

              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-theme-text-primary font-raleway font-medium">₹{vendor.monthlyRevenue.toLocaleString()}</p>
                  <p className="text-theme-text-tertiary font-raleway text-xs">Monthly Revenue</p>
                </div>
                <div>
                  <p className="text-theme-text-primary font-raleway font-medium">₹{vendor.avgOrderValue.toFixed(2)}</p>
                  <p className="text-theme-text-tertiary font-raleway text-xs">Avg Order</p>
                </div>
              </div>

              <div className="border-t border-theme-border-primary pt-4">
                <div className="flex items-center justify-between mb-2">
                  <p className="text-theme-text-secondary font-raleway text-sm">Owner: {vendor.ownerName}</p>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-theme-text-tertiary font-raleway text-xs">{vendor.ownerPhone}</p>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(vendor)}
                      className="p-2 text-theme-text-secondary hover:text-theme-accent-primary hover:bg-theme-accent-primary/10 rounded-lg transition-colors"
                      title="Edit Vendor"
                    >
                      <FaEdit />
                    </button>
                    <button
                      className="p-2 text-theme-text-secondary hover:text-status-info hover:bg-status-info/10 rounded-lg transition-colors"
                      title="View Details"
                    >
                      <FaEye />
                    </button>
                    <button
                      onClick={() => handleDelete(vendor.id)}
                      className="p-2 text-status-error hover:bg-status-error/10 rounded-lg transition-colors"
                      title="Delete Vendor"
                    >
                      <FaTrash />
                    </button>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Add/Edit Vendor Modal */}
        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => e.target === e.currentTarget && resetForm()}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="admin-card rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              >
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-xl font-fredoka text-theme-text-primary">
                    {editingVendor ? 'Edit Vendor' : 'Add New Vendor'}
                  </h2>
                  <button
                    onClick={resetForm}
                    className="text-theme-text-tertiary hover:text-theme-text-primary"
                  >
                    <FaTimes />
                  </button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Image Uploads */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Vendor Logo</label>
                      <ImageUpload
                        currentImage={formData.logo}
                        onImageChange={(imageUrl) => setFormData({ ...formData, logo: imageUrl })}
                        label="Upload Logo"
                        size="medium"
                        shape="circle"
                      />
                    </div>
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Cover Image</label>
                      <ImageUpload
                        currentImage={formData.coverImage}
                        onImageChange={(imageUrl) => setFormData({ ...formData, coverImage: imageUrl })}
                        label="Upload Cover"
                        size="medium"
                        shape="rounded"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Vendor Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                        required
                        placeholder="Enter vendor name"
                      />
                    </div>
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Cuisine Type</label>
                      <input
                        type="text"
                        value={formData.cuisine}
                        onChange={(e) => setFormData({ ...formData, cuisine: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                        required
                        placeholder="Enter cuisine type"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-theme-text-primary font-raleway font-medium mb-2">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                      rows="3"
                      required
                      placeholder="Enter vendor description"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Owner Name</label>
                      <input
                        type="text"
                        value={formData.ownerName}
                        onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                        required
                        placeholder="Enter owner name"
                      />
                    </div>
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Owner Phone</label>
                      <input
                        type="tel"
                        value={formData.ownerPhone}
                        onChange={(e) => setFormData({ ...formData, ownerPhone: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                        required
                        placeholder="Enter phone number"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Owner Email</label>
                      <input
                        type="email"
                        value={formData.ownerEmail}
                        onChange={(e) => setFormData({ ...formData, ownerEmail: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                        required
                        placeholder="Enter email address"
                      />
                    </div>
                    <div>
                      <label className="block text-theme-text-primary font-raleway font-medium mb-2">Status</label>
                      <select
                        value={formData.status}
                        onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                        className="input-theme rounded-lg px-4 py-2 w-full focus:outline-none"
                      >
                        <option value="active">Active</option>
                        <option value="pending">Pending</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex space-x-4 pt-4">
                    <button
                      type="submit"
                      className="flex-1 btn-primary py-2 rounded-lg font-raleway flex items-center justify-center space-x-2"
                    >
                      <FaCheck />
                      <span>{editingVendor ? 'Update Vendor' : 'Add Vendor'}</span>
                    </button>
                    <button
                      type="button"
                      onClick={resetForm}
                      className="flex-1 btn-secondary py-2 rounded-lg font-raleway flex items-center justify-center space-x-2"
                    >
                      <FaTimes />
                      <span>Cancel</span>
                    </button>
                  </div>
                </form>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ZoneAdminLayout>
  );
};

export default AllVendors;
