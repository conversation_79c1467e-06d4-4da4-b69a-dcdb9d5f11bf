import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CartProvider } from './context/CartContext.jsx'; // Ensure the correct extension

// Import simple components that don't use Redux for now
import Login from './pages/Login';
import QRDemo from './components/QRDemo';
import QRTestPage from './components/QRTestPage';

// Super Admin Components
import SuperAdminDashboard from './components/admin/SuperAdminDashboard';
import RestaurantAccounts from './components/admin/accounts/RestaurantAccounts';
import FoodZonesManagement from './components/admin/accounts/FoodZonesManagement';
import Profilemanagement from './components/admin/profiles/ProfileManagement';
import LiveOrders from './components/admin/orders/LiveOrders';
import OrderHistory from './components/admin/orders/OrderHistory';
import AdminSettings from './components/admin/settings/AdminSettings';
import Reports from './components/admin/analytics/Reports';
import AdminProfile from './components/admin/profile/AdminProfile';
import OwnerManagement from './components/admin/accounts/OwnerManagement';
// Removed CustomerManagement - not Super Admin responsibility

// Zone Shop Components
import ZoneShopDashboard from './components/zoneshop/ZoneShopDashboard';
import ZoneShopProfile from './components/zoneshop/ZoneShopProfile';
import MenuItems from './components/zoneshop/menu/MenuItems';
import ZoneShopLiveOrders from './components/zoneshop/orders/LiveOrders';
import ZoneShopInventory from './components/zoneshop/inventory/ZoneShopInventory';
import ZoneShopOrderReports from './components/zoneshop/reports/ZoneShopOrderReports';
import ZoneShopAnalytics from './components/zoneshop/analytics/ZoneShopAnalytics';
import ZoneShopTransactionReports from './components/zoneshop/reports/ZoneShopTransactionReports';
import ZoneShopSettings from './components/zoneshop/settings/ZoneShopSettings';
// Removed TableManagement - not Super Admin responsibility

// Zone Admin Components
import ZoneAdminDashboard from './components/zoneadmin/ZoneAdminDashboard';
import ZoneProfile from './components/zoneadmin/ZoneProfile';
import AllZoneShops from './components/zoneadmin/shops/AllZoneShops';

// Zone Shop Menu Components
import MenuCategories from './components/zoneshop/menu/MenuCategories';
import MenuModifiers from './components/zoneshop/menu/MenuModifiers';
import ZoneShopOrderHistory from './components/zoneshop/orders/OrderHistory';

// Customer Components

// Removed QR, Orders, Menu management - not Super Admin responsibility
import RevenueAnalytics from './components/admin/analytics/RevenueAnalytics';
// Removed ProfileManagement, MenuOverview, CategoriesManagement, ImportExportMenus - not Super Admin responsibility

// Single Restaurant Owner Components
import SingleRestaurantDashboard from './components/owner/SingleRestaurantDashboard';
import QRCodeGenerator from './components/owner/QRCodeGenerator';

// Zone Admin Components
import AllVendors from './components/zoneadmin/vendors/AllVendors';
import ZoneAdminQRCodeGenerator from './components/zoneadmin/QRCodeGenerator';
import ZoneMenuCategories from './components/zoneadmin/menu/MenuCategories';
import ZoneMenuModifiers from './components/zoneadmin/menu/MenuModifiers';
import ZoneMergedMenu from './components/zoneadmin/menu/ZoneMergedMenu';
import ZoneAnalytics from './components/zoneadmin/analytics/ZoneAnalytics';

// Customer Components
import LandingScreen from './pages/customer_user/LandingScreen';
import DigitalMenuScreen from './pages/customer_user/DigitalMenuScreen';
import ProductDetailScreen from './pages/customer_user/ProductDetailScreen';
import CartScreen from './pages/customer_user/CartScreen';
import OTPLoginScreen from './pages/customer_user/OTPLoginScreen';
import CheckoutScreen from './pages/customer_user/CheckoutScreen';
import OrderSuccessScreen from './pages/customer_user/OrderSuccessScreen';
import OrderTrackingScreen from './pages/customer_user/OrderTrackingScreen';
import ZoneShopSelection from './components/customer/ZoneShopSelection';

// Zone User Components
import ZoneShopSelectionScreen from './pages/zone_user/ZoneShopSelectionScreen';
import ZoneDigitalMenuScreen from './pages/zone_user/ZoneDigitalMenuScreen';
import ZoneCartScreen from './pages/zone_user/ZoneCartScreen';

import AdminBilling from './components/admin/AdminBilling';
import MenuManagementPage from './pages/owner/MenuManagement';
import MenuCategoriesPage from './pages/owner/MenuCategories';
import MenuModifiersPage from './pages/owner/MenuModifiers';
import ProfileManagementPage from './pages/owner/ProfileManagement';
import OrderManagementPage from './pages/owner/OrderManagement';
import TableManagementPage from './pages/owner/TableManagement';
import AnalyticsPage from './pages/owner/Analytics';
import SettingsPage from './pages/owner/Settings';

// Core components
import ScrollToTop from './components/ScrollToTop';

// Marketing website components
import HomePage from './screens/HomePage';
import Services from './screens/Services';
import About from './screens/About';
import Contact from './screens/Contact';
import ChoosePlan from './pages/subscription/ChoosePlan';
import LoginGuard from './components/subscription/LoginGuard';
import Signup from './pages/Signup';



import Layout from './components/Layout';

import AOS from 'aos';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { initializeTheme } from './store/slices/themeSlice';
import ThemeToggle from './components/common/ThemeToggle';
import 'aos/dist/aos.css';

function AppContent() {
  const dispatch = useDispatch();

  useEffect(() => {
    AOS.init({ duration: 1000 });
    // Initialize theme on app startup
    dispatch(initializeTheme());
  }, [dispatch]);
  {/* Signup route - also guarded by plan selection */ }
  <Route path="/tableserve/signup" element={<LoginGuard><Signup /></LoginGuard>} />


  return (
    <Router>
      <ScrollToTop />
      <Routes>
        {/* Plan selection page (must choose plan before login) */}
        {/* Root redirect to marketing site */}
        <Route path="/" element={<Navigate to="/tableserve" replace />} />

        <Route path="/tableserve/choose-plan" element={<ChoosePlan />} />

        {/* Signup route - plan must be chosen first */}
        <Route path="/tableserve/signup" element={<LoginGuard><Signup /></LoginGuard>} />
        {/* Login route - plan must be chosen first */}
        <Route path="/tableserve/login" element={<LoginGuard><Login /></LoginGuard>} />

        {/* Marketing Website Routes */}
        <Route path="/tableserve" element={
          <Layout>
            <HomePage />
          </Layout>
        } />
        <Route path="tableserve/services" element={
          <Layout>
            <Services />
          </Layout>
        } />
        <Route path="tableserve/about" element={
          <Layout>
            <About />
          </Layout>
        } />
        <Route path="tableserve/contact" element={
          <Layout>
            <Contact />
          </Layout>
        } />

        {/* QR Demo Routes */}
        <Route path="tableserve/qr-demo" element={<QRDemo />} />
        <Route path="tableserve/qr-test" element={<QRTestPage />} />

        {/* Theme Test Route */}
        <Route path="/theme-test" element={
          <div className="min-h-screen admin-layout theme-transition p-8">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-3xl font-fredoka text-theme-text-primary mb-6">Theme System Test</h1>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="admin-card p-6 rounded-lg">
                  <h2 className="text-xl font-raleway text-theme-text-primary mb-4">Theme Toggle</h2>
                  <ThemeToggle />
                </div>
                <div className="admin-card p-6 rounded-lg">
                  <h2 className="text-xl font-raleway text-theme-text-primary mb-4">Color Samples</h2>
                  <div className="space-y-2">
                    <div className="p-2 bg-theme-accent-primary text-theme-text-inverse rounded">Accent Primary</div>
                    <div className="p-2 bg-theme-bg-secondary text-theme-text-secondary rounded">Background Secondary</div>
                    <div className="p-2 border border-theme-border-primary text-theme-text-primary rounded">Border Primary</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        } />

        {/* Authentication - Single Login Portal */}
        {/* Guard: if no subscription chosen, redirect to choose-plan before login */}
        <Route path="/tableserve/login" element={<LoginGuard><Login /></LoginGuard>} />

        <Route path="/tableserve/login" element={<Login />} />

        {/* ===== SUPER ADMIN ROUTES (HIGHEST PRIORITY) ===== */}
        <Route path="/tableserve/admin/dashboard" element={<SuperAdminDashboard />} />
        <Route path="/tableserve/admin/profiles" element={<Profilemanagement />} />
        <Route path="/tableserve/admin/accounts/restaurants" element={<RestaurantAccounts />} />
        <Route path="/tableserve/admin/accounts/zones" element={<FoodZonesManagement />} />
        <Route path="/tableserve/admin/accounts/owners" element={<OwnerManagement />} />
        <Route path="/tableserve/admin/orders/live" element={<LiveOrders />} />
        <Route path="/tableserve/admin/orders/history" element={<OrderHistory />} />
        <Route path="/tableserve/admin/analytics/overview" element={<RevenueAnalytics />} />
        <Route path="/tableserve/admin/analytics/revenue" element={<RevenueAnalytics />} />
        <Route path="/tableserve/admin/analytics/reports" element={<Reports />} />
        <Route path="/tableserve/admin/profile" element={<AdminProfile />} />
        <Route path="/tableserve/admin/settings" element={<AdminSettings />} />
        <Route path="/tableserve/admin/billing" element={<AdminBilling />} />

        {/* ===== ZONE ADMIN ROUTES (SECOND PRIORITY) ===== */}
        <Route path="/tableserve/zone/:zoneId/dashboard" element={<ZoneAdminDashboard />} />
        <Route path="/tableserve/zone/:zoneId/profile" element={<ZoneProfile />} />
        <Route path="/tableserve/zone/:zoneId/shops" element={<AllZoneShops />} />
        <Route path="/tableserve/zone/:zoneId/shops/add" element={<AllZoneShops />} />
        <Route path="/tableserve/zone/:zoneId/shops/revenue" element={<AllZoneShops />} />
        <Route path="/tableserve/zone/:zoneId/shops/performance" element={<AllZoneShops />} />
        <Route path="/tableserve/zone/:zoneId/vendors" element={<AllZoneShops />} />
        <Route path="/tableserve/zone/:zoneId/vendors/list" element={<AllVendors />} />
        <Route path="/tableserve/zone/:zoneId/menu/categories" element={<ZoneMenuCategories />} />
        <Route path="/tableserve/zone/:zoneId/menu/modifiers" element={<ZoneMenuModifiers />} />
        <Route path="/tableserve/zone/:zoneId/menu/merged" element={<ZoneMergedMenu />} />
        <Route path="/tableserve/zone/:zoneId/analytics/revenue" element={<ZoneAnalytics />} />
        <Route path="/tableserve/zone/:zoneId/analytics/performance" element={<ZoneAnalytics />} />
        <Route path="/tableserve/zone/:zoneId/qr/generator" element={<ZoneAdminQRCodeGenerator />} />

        {/* ===== ZONE SHOP ROUTES (THIRD PRIORITY) ===== */}
        <Route path="/tableserve/zone/:zoneId/shop/:shopId" element={<Navigate to="dashboard" replace />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/dashboard" element={<ZoneShopDashboard />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/profile" element={<ZoneShopProfile />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/menu/items" element={<MenuItems />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/menu/categories" element={<MenuCategories />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/menu/modifiers" element={<MenuModifiers />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/inventory" element={<ZoneShopInventory />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/orders/live" element={<ZoneShopLiveOrders />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/orders/history" element={<ZoneShopOrderHistory />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/orders/reports" element={<ZoneShopOrderReports />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/analytics/revenue" element={<ZoneShopAnalytics />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/analytics/performance" element={<ZoneShopAnalytics />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/reports/transactions" element={<ZoneShopTransactionReports />} />
        <Route path="/tableserve/zone/:zoneId/shop/:shopId/settings" element={<ZoneShopSettings />} />

        {/* ===== RESTAURANT OWNER ROUTES (FOURTH PRIORITY - MOST SPECIFIC) ===== */}
        <Route path="/tableserve/restaurant/:restaurantId/dashboard" element={<SingleRestaurantDashboard />} />
        <Route path="/tableserve/restaurant/:restaurantId/profile" element={<ProfileManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/menu/items" element={<MenuManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/menu/categories" element={<MenuCategoriesPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/menu/modifiers" element={<MenuModifiersPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/tables" element={<TableManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/qr/generator" element={<QRCodeGenerator />} />
        <Route path="/tableserve/restaurant/:restaurantId/orders/live" element={<OrderManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/orders/history" element={<OrderManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/orders/feedback" element={<OrderManagementPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/analytics/sales" element={<AnalyticsPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/analytics/revenue" element={<AnalyticsPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/analytics/export" element={<AnalyticsPage />} />
        <Route path="/tableserve/restaurant/:restaurantId/settings" element={<SettingsPage />} />

        {/* ===== CUSTOMER-FACING ROUTES (QR CODE SYSTEM) ===== */}
        {/* Restaurant Customer Routes - Table-based QR System */}
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/menu" element={<DigitalMenuScreen />} />
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/product/:productId" element={<ProductDetailScreen />} />
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/cart" element={<CartScreen />} />
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/checkout" element={<CheckoutScreen />} />
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/success" element={<OrderSuccessScreen />} />
        <Route path="/tableserve/restaurant/:restaurantId/table/:tableId/tracking" element={<OrderTrackingScreen />} />

        {/* Zone Customer Routes - Zone-Wide Table System */}
        <Route path="/tableserve/zone/:zoneId/table/:tableId/shops" element={<ZoneShopSelection />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/shop/:shopId/menu" element={<DigitalMenuScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/shop/:shopId/product/:productId" element={<ProductDetailScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/cart" element={<CartScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/checkout" element={<CheckoutScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/success" element={<OrderSuccessScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/tracking" element={<OrderTrackingScreen />} />

        {/* ===== ZONE USER ROUTES (NEW PATTERN) ===== */}
        {/* Zone User Routes - /tableserve/zone/:zoneId/table/:tableId/user/:userId/* */}
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId" element={<Navigate to="shops" replace />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/shops" element={<ZoneShopSelectionScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/menu" element={<ZoneDigitalMenuScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/shop/:shopId/menu" element={<ZoneDigitalMenuScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/cart" element={<ZoneCartScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/checkout" element={<CheckoutScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/success" element={<OrderSuccessScreen />} />
        <Route path="/tableserve/zone/:zoneId/table/:tableId/user/:userId/tracking" element={<OrderTrackingScreen />} />

        {/* Enhanced QR System Routes - Multiple URL patterns supported */}

        {/* User-side Routes - /tableserve/:restaurantName/:userId/* */}
        <Route path="/tableserve/:restaurantName/:userId/home" element={<LandingScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/menu" element={<DigitalMenuScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/product/:dishId" element={<ProductDetailScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/cart" element={<CartScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/otp-login" element={<OTPLoginScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/checkout" element={<CheckoutScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/order-success" element={<OrderSuccessScreen />} />
        <Route path="/tableserve/:restaurantName/:userId/order-tracking" element={<OrderTrackingScreen />} />

        {/* QR Code Scanning Redirection */}
        <Route
          path="/tableserve/:restaurantName/:userId"
          element={<Navigate to="home" replace />}
        />

        {/* Legacy redirects */}
        <Route path="/tableserve/admin" element={<Navigate to="/admin/dashboard" />} />
        <Route path="/tableserve/login" element={<Navigate to="/tableserve/login" />} />
        <Route path="/tableserve/admin/login" element={<Navigate to="/tableserve/login" />} />
        <Route path="/tableserve/owner/login" element={<Navigate to="/tableserve/login" />} />
        <Route path="/tableserve/zone-admin/login" element={<Navigate to="/tableserve/login" />} />
        <Route path="/tableserve/zone-shop/login" element={<Navigate to="/tableserve/login" />} />
      </Routes>
    </Router>
  );
}

export default function App() {
  return (
    <CartProvider>
      <AppContent />
    </CartProvider>
  );
}