import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import LocalStorageService from '../services/LocalStorageService';
import SubscriptionService from '../services/SubscriptionService';

const initialForm = {
  name: '',
  ownerName: '',
  ownerEmail: '',
  ownerPhone: '',
  username: '',
  password: '',
};

export default function Signup() {
  const navigate = useNavigate();
  const [plan, setPlan] = useState(null); // { planType, plan, maxTables, ... }
  const [form, setForm] = useState(initialForm);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const sub = SubscriptionService.get();
    setPlan(sub);
  }, []);

  const onChange = (e) => {
    setForm((f) => ({ ...f, [e.target.name]: e.target.value }));
    if (error) setError(null);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!plan) {
      navigate('/tableserve/choose-plan');
      return;
    }

    if (!form.username || !form.password || !form.name || !form.ownerName) {
      setError('Please fill all required fields');
      return;
    }

    setLoading(true);
    try {
      if (plan.planType === 'restaurant') {
        const newRestaurant = LocalStorageService.addRestaurant({
          name: form.name,
          ownerName: form.ownerName,
          ownerEmail: form.ownerEmail,
          ownerPhone: form.ownerPhone,
          status: 'active',
          loginCredentials: {
            username: form.username,
            password: form.password,
          },
          subscription: plan,
        });
        alert('Restaurant account created successfully. Please log in.');
        navigate('/tableserve/login');
      } else if (plan.planType === 'zone') {
        const newZone = LocalStorageService.addZone({
          name: form.name,
          ownerName: form.ownerName,
          ownerEmail: form.ownerEmail,
          ownerPhone: form.ownerPhone,
          status: 'active',
          loginCredentials: {
            username: form.username,
            password: form.password,
          },
          subscription: plan,
        });
        alert('Zone account created successfully. Please log in.');
        navigate('/tableserve/login');
      } else {
        setError('Invalid plan type.');
      }
    } catch (err) {
      console.error(err);
      setError('Failed to create account.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 signup-form">
      <div className="bg-white p-6 rounded-lg border w-full max-w-md">
        <div className="text-center mb-4">
          <h1 className="text-2xl font-fredoka text-accent">Create your account</h1>
          <p className="text-sm text-gray-600">Plan: {plan?.planType ? plan.planType.toUpperCase() : 'Not selected'}</p>
        </div>

        {!plan && (
          <div className="p-3 mb-4 text-sm text-gray-700 bg-yellow-50 border border-yellow-200 rounded">
            You must choose a plan first.
            <button onClick={() => navigate('/tableserve/choose-plan')} className="ml-2 text-accent underline">Choose Plan</button>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-3">
          <div>
            <label className="block text-sm text-gray-700 mb-1">{plan?.planType === 'zone' ? 'Zone Name' : 'Restaurant Name'} *</label>
            <input name="name" value={form.name} onChange={onChange} className="w-full border  rounded px-3 py-2 autofill-protected" />
          </div>
          <div className="grid grid-cols-1 gap-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">Owner Name *</label>
              <input name="ownerName" value={form.ownerName} onChange={onChange} className="w-full border rounded px-3 py-2 autofill-protected" />
            </div>
            <div>
              <label className="block text-sm text-gray-700 mb-1">Owner Email</label>
              <input type="email" name="ownerEmail" value={form.ownerEmail} onChange={onChange} className="w-full border rounded px-3 py-2 autofill-protected" />
            </div>
            <div>
              <label className="block text-sm text-gray-700 mb-1">Owner Phone</label>
              <input name="ownerPhone" value={form.ownerPhone} onChange={onChange} className="w-full border rounded px-3 py-2 autofill-protected" />
            </div>
          </div>

          <div className="grid grid-cols-1 gap-3">
            <div>
              <label className="block text-sm text-gray-700 mb-1">Username *</label>
              <input name="username" value={form.username} onChange={onChange} className="w-full border rounded px-3 py-2 autofill-protected" />
            </div>
            <div>
              <label className="block text-sm text-gray-700 mb-1">Password *</label>
              <input type="password" name="password" value={form.password} onChange={onChange} className="w-full border rounded px-3 py-2 autofill-protected" />
            </div>
          </div>

          {error && <div className="text-sm text-red-600">{error}</div>}

          <button disabled={!plan || loading} type="submit" className="w-full bg-accent text-white py-2 rounded disabled:opacity-60">
            {loading ? 'Creating...' : 'Create Account'}
          </button>
        </form>

        <div className="text-center mt-3">
          <button onClick={() => navigate('/tableserve/login')} className="text-sm text-accent">Already have an account? Login</button>
        </div>
      </div>
    </div>
  );
}

