import React, { useState } from 'react';
// Choose Plan: persisted before login
import { motion } from 'framer-motion';
import { FaCheck, FaTimes, FaUsers, FaTable } from 'react-icons/fa';

import { useDispatch } from 'react-redux';
import { setSubscription } from '../../store/slices/subscriptionSlice';
import { RESTAURANT_PLANS, ZONE_PLANS, resolvePlanMetadata } from '../../constants/plans';
import { useNavigate } from 'react-router-dom';

export default function ChoosePlan() {
  const [planType, setPlanType] = useState('restaurant');
  const [planKey, setPlanKey] = useState('basic');
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const plans = planType === 'zone' ? ZONE_PLANS : RESTAURANT_PLANS;

  const handleContinue = () => {
    const metadata = resolvePlanMetadata({ planKey, planType });
    // TODO: integrate payment gateway before setting subscription
    dispatch(setSubscription(metadata));
    // Persist locally so guards can enforce
    try { localStorage.setItem('tableserve_subscription', JSON.stringify(metadata)); } catch { }
    // redirect to signup flow to let the client create their account
    navigate('/tableserve/signup');
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-6">
      <div className="w-full max-w-6xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-fredoka text-accent">Choose your plan</h1>
          <p className="text-primary-bg font-raleway mt-1">Select a plan based on your business type. You can upgrade anytime.</p>
        </div>

        {/* Toggle */}
        <div className="flex justify-center mb-6">
          <div className="inline-flex bg-accent/20 border border-theme-border rounded-full p-1">
            <button onClick={() => setPlanType('restaurant')} className={`px-4 py-2 rounded-full font-raleway text-sm ${planType === 'restaurant' ? 'bg-accent text-white' : 'text-black '}`}>Restaurant</button>
            <button onClick={() => setPlanType('zone')} className={`px-4 py-2 rounded-full font-raleway text-sm ${planType === 'zone' ? 'bg-accent text-white' : 'text-black'}`}>Zone</button>
          </div>
        </div>

        {/* Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
          {Object.values(plans).map((p) => {
            const selected = planKey === p.key;
            return (
              <label key={p.key} className={`relative group block rounded-2xl border transition-all cursor-pointer ${selected ? 'border-accent shadow-[0_2px_5px_rgb(235_61_30_/0.25)]' : 'border-theme-border hover:border-accent/60'}`}>
                <input type="radio" name="planKey" value={p.key} checked={selected} onChange={() => setPlanKey(p.key)} className="hidden" />
                <div className="p-5">
                  <div className="flex items-center justify-between ">
                    <h3 className="text-xl font-raleway font-semibold mb-4 text-theme-text-primary">{p.label}</h3>
                    {selected && <span className="text-xs bg-accent text-white px-2 py-1 rounded-full">Selected</span>}
                  </div>

                  <ul className="space-y-2 text-sm font-raleway -mt-4">
                    {planType === 'restaurant' ? (
                      <>
                        <li className="flex items-center gap-2"><FaTable className="text-accent" /> Max Tables: <strong>{p.maxTables}</strong></li>
                        <li className="flex items-center gap-2"><FaCheck className="text-green-600" /> CRUD Menu</li>
                        <li className="flex items-center gap-2"><FaCheck className="text-green-600" />  Analytics</li>
                      </>
                    ) : (
                      <>
                        <li className="flex items-center gap-2"><FaTable className="text-accent" /> Max Tables: <strong>{p.maxTables ?? 'Custom'}</strong></li>
                        <li className="flex items-center gap-2"><FaUsers className="text-accent" /> Max Vendors: <strong>{p.maxVendors ?? 'Custom'}</strong></li>

                        <li className="flex items-center gap-2">{p.features.analytics ? <FaCheck className="text-green-600" /> : <FaTimes className="text-red-500" />} Analytics</li>
                      </>
                    )}
                  </ul>
                </div>
              </label>
            );
          })}
        </div>

        <div className="flex justify-end mt-6">
          <button onClick={handleContinue} className="px-6 py-3 bg-accent text-white rounded-lg font-raleway font-semibold shadow hover:bg-accent/90">Continue</button>
        </div>
      </div>
    </div>
  );
}

