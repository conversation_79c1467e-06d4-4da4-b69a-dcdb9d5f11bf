import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { FaShoppingCart, FaArrowLeft } from 'react-icons/fa';

const ZoneUserNavbar = ({ cartItemCount = 0, showBackButton = false, title = "Food Zone" }) => {
  const { zoneId, tableId, userId } = useParams();
  const navigate = useNavigate();

  const handleBackClick = () => {
    navigate(-1);
  };

  const handleCartClick = () => {
    navigate(`/tableserve/zone/${zoneId}/table/${tableId}/user/${userId}/cart`);
  };

  return (
    <nav className="bg-white shadow-md sticky top-0 z-50 border-b border-divider-border">
      <div className="max-w-md mx-auto px-4 py-3">
        <div className="flex items-center justify-between">
          {/* Left side - Back button or Logo */}
          <div className="flex items-center">
            {showBackButton ? (
              <motion.button
                whileTap={{ scale: 0.9 }}
                onClick={handleBackClick}
                className="p-2 bg-accent rounded-full shadow-lg mr-3"
              >
                <FaArrowLeft className="text-lg text-white" />
              </motion.button>
            ) : (
              <div className="flex items-center">
                <div className="w-10 h-10 bg-accent rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-fredoka text-lg">FZ</span>
                </div>
                <div>
                  <h1 className="text-lg font-fredoka text-primary-bg">{title}</h1>
                  <p className="text-xs text-placeholder-subtext">Table #{tableId}</p>
                </div>
              </div>
            )}
          </div>

          {/* Right side - Cart */}
          {cartItemCount > 0 && (
            <motion.button
              whileTap={{ scale: 0.9 }}
              onClick={handleCartClick}
              className="relative p-2 bg-accent rounded-full shadow-lg"
            >
              <FaShoppingCart className="text-lg text-white" />
              <span className="absolute -top-1 -right-1 bg-primary-bg text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                {cartItemCount}
              </span>
            </motion.button>
          )}
        </div>
      </div>
    </nav>
  );
};

export default ZoneUserNavbar;
