import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { store } from './store';
import './index.css';
import App from './App.jsx';

// Initialize theme on app startup
const savedTheme = localStorage.getItem('tableserve-theme') || 'dark';
document.documentElement.classList.add(savedTheme);
import SubscriptionService from './services/SubscriptionService';
import { setSubscription } from './store/slices/subscriptionSlice';

// Hydrate subscription into Redux on boot
const sub = SubscriptionService.get();
if (sub) {
  store.dispatch(setSubscription(sub));
}


createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </StrictMode>,
);
