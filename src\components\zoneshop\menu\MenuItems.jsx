import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaImage,
  FaUpload,
  FaCheckCircle,
  FaTimesCircle,
  FaStar
} from 'react-icons/fa';
import ZoneShopLayout from '../ZoneShopLayout';
import ImageUpload from '../../common/ImageUpload';

const MenuItems = () => {
  const [menuItems, setMenuItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: '',
    image: '',
    preparationTime: '',
    available: true,
    popular: false,
    ingredients: '',
    allergens: ''
  });

  // Mock data
  const mockMenuItems = [
    {
      id: 1,
      name: 'Margherita Pizza',
      description: 'Fresh mozzarella, tomato sauce, basil',
      price: 18.99,
      category: 'Pizza',
      image: 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300',
      preparationTime: 15,
      available: true,
      popular: true,
      ingredients: 'Mozzarella, Tomato Sauce, Basil, Dough',
      allergens: 'Gluten, Dairy',
      orders: 45,
      rating: 4.8
    },
    {
      id: 2,
      name: 'Pepperoni Pizza',
      description: 'Classic pepperoni with mozzarella cheese',
      price: 21.99,
      category: 'Pizza',
      image: 'https://images.unsplash.com/photo-1628840042765-356cda07504e?w=300',
      preparationTime: 18,
      available: true,
      popular: true,
      ingredients: 'Pepperoni, Mozzarella, Tomato Sauce, Dough',
      allergens: 'Gluten, Dairy',
      orders: 38,
      rating: 4.7
    },
    {
      id: 3,
      name: 'Garlic Bread',
      description: 'Crispy bread with garlic butter and herbs',
      price: 6.99,
      category: 'Sides',
      image: 'https://images.unsplash.com/photo-1573140247632-f8fd74997d5c?w=300',
      preparationTime: 8,
      available: false,
      popular: false,
      ingredients: 'Bread, Garlic, Butter, Herbs',
      allergens: 'Gluten, Dairy',
      orders: 22,
      rating: 4.5
    }
  ];

  const categories = ['Pizza', 'Sides', 'Beverages', 'Desserts'];

  useEffect(() => {
    setTimeout(() => {
      setMenuItems(mockMenuItems);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || item.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    if (editingItem) {
      setMenuItems(items =>
        items.map(item =>
          item.id === editingItem.id
            ? { ...item, ...formData, price: parseFloat(formData.price), preparationTime: parseInt(formData.preparationTime) }
            : item
        )
      );
    } else {
      const newItem = {
        ...formData,
        id: Date.now(),
        price: parseFloat(formData.price),
        preparationTime: parseInt(formData.preparationTime),
        orders: 0,
        rating: 0
      };
      setMenuItems([newItem, ...menuItems]);
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: '',
      category: '',
      image: '',
      preparationTime: '',
      available: true,
      popular: false,
      ingredients: '',
      allergens: ''
    });
    setEditingItem(null);
    setShowForm(false);
  };

  const handleEdit = (item) => {
    setFormData({
      name: item.name,
      description: item.description,
      price: item.price.toString(),
      category: item.category,
      image: item.image,
      preparationTime: item.preparationTime.toString(),
      available: item.available,
      popular: item.popular,
      ingredients: item.ingredients,
      allergens: item.allergens
    });
    setEditingItem(item);
    setShowForm(true);
  };

  const handleDelete = (itemId) => {
    if (window.confirm('Are you sure you want to delete this menu item?')) {
      setMenuItems(items => items.filter(item => item.id !== itemId));
    }
  };

  const toggleAvailability = (itemId) => {
    setMenuItems(items =>
      items.map(item =>
        item.id === itemId
          ? { ...item, available: !item.available }
          : item
      )
    );
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-primary text-xl">Loading menu items...</div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2">Menu Items</h1>
            <p className="text-primary font-raleway">Manage your restaurant's menu items</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2"
          >
            <FaPlus />
            <span>Add Menu Item</span>
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div className="bg-primary backdrop-blur-lg rounded-xl p-4 border border-primary">
            <h3 className="text-2xl font-fredoka text-primary">{menuItems.length}</h3>
            <p className="text-primary font-raleway text-sm">Total Items</p>
          </div>
          <div className="bg-primary backdrop-blur-lg rounded-xl p-4 border border-primary">
            <h3 className="text-2xl font-fredoka text-green-400">{menuItems.filter(i => i.available).length}</h3>
            <p className="text-primary font-raleway text-sm">Available</p>
          </div>
          <div className="bg-primary backdrop-blur-lg rounded-xl p-4 border border-primary">
            <h3 className="text-2xl font-fredoka text-yellow-400">{menuItems.filter(i => i.popular).length}</h3>
            <p className="text-primary font-raleway text-sm">Popular</p>
          </div>
          <div className="bg-primary backdrop-blur-lg rounded-xl p-4 border border-primary">
            <h3 className="text-2xl font-fredoka text-primary">{categories.length}</h3>
            <p className="text-primary font-raleway text-sm">Categories</p>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-primary backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
              <input
                type="text"
                placeholder="Search menu items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-primary border border-primary rounded-lg pl-10 pr-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
              />
            </div>
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Menu Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className={`bg-primary backdrop-blur-lg rounded-2xl overflow-hidden border border-primary ${!item.available ? 'opacity-60' : ''
                }`}
            >
              {/* Item Image */}
              <div className="relative h-48 bg-primary">
                {item.image ? (
                  <img src={item.image} alt={item.name} className="w-full h-full object-cover" />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <FaImage className="text-primary text-4xl" />
                  </div>
                )}
                <div className="absolute top-3 right-3 flex space-x-2">
                  {item.popular && (
                    <span className="bg-yellow-500 text-primary px-2 py-1 rounded-full text-xs font-raleway">
                      <FaStar className="inline mr-1" />
                      Popular
                    </span>
                  )}
                  <button
                    onClick={() => toggleAvailability(item.id)}
                    className={`p-2 rounded-full ${item.available
                      ? 'bg-green-500 text-primary'
                      : 'bg-red-500 text-primary'
                      }`}
                  >
                    {item.available ? <FaCheckCircle /> : <FaTimesCircle />}
                  </button>
                </div>
              </div>

              {/* Item Details */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="text-primary font-fredoka text-lg">{item.name}</h3>
                    <p className="text-primary font-raleway text-sm">{item.category}</p>
                  </div>
                  <p className="text-accent font-fredoka text-xl">₹{item.price}</p>
                </div>

                <p className="text-primary font-raleway text-sm mb-3 line-clamp-2">{item.description}</p>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4 text-sm text-primary">
                    <span>{item.preparationTime} min</span>
                    <span>{item.orders} orders</span>
                    {item.rating > 0 && (
                      <div className="flex items-center space-x-1">
                        <FaStar className="text-yellow-400" />
                        <span>{item.rating}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(item)}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 text-primary py-2 px-3 rounded-lg font-raleway text-sm flex items-center justify-center space-x-2"
                  >
                    <FaEdit />
                    <span>Edit</span>
                  </button>
                  <button className="flex-1 bg-green-500 hover:bg-green-600 text-primary py-2 px-3 rounded-lg font-raleway text-sm flex items-center justify-center space-x-2">
                    <FaEye />
                    <span>View</span>
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    className="bg-red-500 hover:bg-red-600 text-primary py-2 px-3 rounded-lg"
                  >
                    <FaTrash />
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Add/Edit Item Modal */}
        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-transparent/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => e.target === e.currentTarget && resetForm()}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="admin-card backdrop-blur-xl border border-primary rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              >
                <h2 className="text-2xl font-fredoka text-primary mb-6">
                  {editingItem ? 'Edit Menu Item' : 'Add New Menu Item'}
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Image Upload */}
                  <ImageUpload
                    currentImage={formData.image}
                    onImageChange={(imageUrl) => setFormData({ ...formData, image: imageUrl })}
                    label="Item Image"
                    size="large"
                    shape="rounded"
                  />

                  {/* Basic Info */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Item Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Price (₹)</label>
                      <input
                        type="number"
                        step="0.01"
                        value={formData.price}
                        onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                        className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                      rows="3"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Category</label>
                      <select
                        value={formData.category}
                        onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                        className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        required
                      >
                        <option value="">Select Category</option>
                        {categories.map(category => (
                          <option key={category} value={category}>{category}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Preparation Time (min)</label>
                      <input
                        type="number"
                        value={formData.preparationTime}
                        onChange={(e) => setFormData({ ...formData, preparationTime: e.target.value })}
                        className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Ingredients</label>
                    <input
                      type="text"
                      value={formData.ingredients}
                      onChange={(e) => setFormData({ ...formData, ingredients: e.target.value })}
                      className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                      placeholder="Comma-separated ingredients"
                    />
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Allergens</label>
                    <input
                      type="text"
                      value={formData.allergens}
                      onChange={(e) => setFormData({ ...formData, allergens: e.target.value })}
                      className="w-full bg-primary border border-primary rounded-lg px-4 py-2 text-primary focus:outline-none focus:border-accent"
                      placeholder="Comma-separated allergens"
                    />
                  </div>

                  <div className="flex items-center space-x-6">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.available}
                        onChange={(e) => setFormData({ ...formData, available: e.target.checked })}
                        className="w-4 h-4 text-accent bg-primary border-primary rounded focus:ring-accent"
                      />
                      <span className="text-primary font-raleway">Available</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.popular}
                        onChange={(e) => setFormData({ ...formData, popular: e.target.checked })}
                        className="w-4 h-4 text-accent bg-primary border-primary rounded focus:ring-accent"
                      />
                      <span className="text-primary font-raleway">Popular Item</span>
                    </label>
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={resetForm}
                      className="px-6 py-2 bg-primary hover:bg-primary text-primary rounded-lg font-raleway transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-accent hover:bg-accent/90 text-white rounded-lg font-raleway transition-colors"
                    >
                      {editingItem ? 'Update Item' : 'Add Item'}
                    </button>
                  </div>
                </form>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </ZoneShopLayout>
  );
};

export default MenuItems;
