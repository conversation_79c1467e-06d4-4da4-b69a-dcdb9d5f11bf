import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaRupeeSign,
  FaShoppingCart,
  FaStar,
  FaUsers,
  FaChartLine,
  FaCalendarAlt,
  FaClock,
  FaUtensils
} from 'react-icons/fa';
import ZoneShopLayout from '../ZoneShopLayout';

const ZoneShopAnalytics = () => {
  const { zoneId, shopId } = useParams();
  const [analytics, setAnalytics] = useState({});
  const [timeRange, setTimeRange] = useState('today');
  const [loading, setLoading] = useState(true);

  // Mock analytics data
  const mockAnalytics = {
    today: {
      revenue: 2450,
      orders: 18,
      avgOrderValue: 136,
      rating: 4.6,
      customers: 15,
      peakHour: '7:00 PM',
      topItems: [
        { name: 'Margherita Pizza', orders: 8, revenue: 2392 },
        { name: 'Pepperoni Pizza', orders: 5, revenue: 1745 },
        { name: 'Garlic Bread', orders: 12, revenue: 1068 }
      ],
      hourlyData: [
        { hour: '9 AM', orders: 2, revenue: 450 },
        { hour: '10 AM', orders: 1, revenue: 299 },
        { hour: '11 AM', orders: 3, revenue: 847 },
        { hour: '12 PM', orders: 4, revenue: 1196 },
        { hour: '1 PM', orders: 3, revenue: 897 },
        { hour: '2 PM', orders: 2, revenue: 598 },
        { hour: '7 PM', orders: 6, revenue: 1794 },
        { hour: '8 PM', orders: 4, revenue: 1196 },
        { hour: '9 PM', orders: 3, revenue: 897 }
      ],
      trends: {
        revenue: 12.5,
        orders: 8.3,
        avgOrderValue: 4.2,
        rating: 0.2
      }
    },
    week: {
      revenue: 15680,
      orders: 124,
      avgOrderValue: 126,
      rating: 4.5,
      customers: 98,
      peakDay: 'Saturday',
      topItems: [
        { name: 'Margherita Pizza', orders: 45, revenue: 13455 },
        { name: 'Pepperoni Pizza', orders: 32, revenue: 11168 },
        { name: 'Veggie Supreme', orders: 28, revenue: 9772 }
      ],
      dailyData: [
        { day: 'Mon', orders: 15, revenue: 1890 },
        { day: 'Tue', orders: 18, revenue: 2268 },
        { day: 'Wed', orders: 16, revenue: 2016 },
        { day: 'Thu', orders: 20, revenue: 2520 },
        { day: 'Fri', orders: 22, revenue: 2772 },
        { day: 'Sat', orders: 25, revenue: 3150 },
        { day: 'Sun', orders: 19, revenue: 2394 }
      ],
      trends: {
        revenue: 18.7,
        orders: 15.2,
        avgOrderValue: 3.1,
        rating: -0.1
      }
    },
    month: {
      revenue: 68450,
      orders: 542,
      avgOrderValue: 126,
      rating: 4.4,
      customers: 387,
      peakWeek: 'Week 3',
      topItems: [
        { name: 'Margherita Pizza', orders: 198, revenue: 59202 },
        { name: 'Pepperoni Pizza', orders: 145, revenue: 50605 },
        { name: 'Veggie Supreme', orders: 132, revenue: 46068 }
      ],
      weeklyData: [
        { week: 'Week 1', orders: 125, revenue: 15750 },
        { week: 'Week 2', orders: 138, revenue: 17388 },
        { week: 'Week 3', orders: 156, revenue: 19656 },
        { week: 'Week 4', orders: 123, revenue: 15498 }
      ],
      trends: {
        revenue: 22.3,
        orders: 19.8,
        avgOrderValue: 2.1,
        rating: -0.2
      }
    }
  };

  useEffect(() => {
    // Simulate loading analytics data
    setTimeout(() => {
      setAnalytics(mockAnalytics);
      setLoading(false);
    }, 1000);
  }, []);

  const currentData = analytics[timeRange] || {};



  const getTrendColor = (trend) => {
    return trend > 0 ? 'text-green-500' : trend < 0 ? 'text-red-500' : 'text-theme-text-tertiary';
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-theme-accent-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-secondary">Loading analytics...</p>
          </div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-primary mb-2">
              Analytics & Performance
            </h1>
            <p className="text-theme-text-secondary font-raleway">
              Track your shop's performance and revenue insights
            </p>
          </div>
          <div className="flex items-center space-x-2 mt-4 sm:mt-0">
            <FaCalendarAlt className="text-theme-text-tertiary" />
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 border border-theme-border-primary rounded-lg bg-theme-bg-secondary text-theme-text-primary focus:outline-none focus:ring-2 focus:ring-theme-accent-primary"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
            </select>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-5 gap-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center justify-between mb-2">
              <FaRupeeSign className="text-theme-accent-primary text-xl" />

            </div>
            <p className="text-2xl font-fredoka text-theme-text-primary">₹{currentData.revenue?.toLocaleString()}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Revenue</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center justify-between mb-2">
              <FaShoppingCart className="text-blue-500 text-xl" />

            </div>
            <p className="text-2xl font-fredoka text-theme-text-primary">{currentData.orders}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Orders</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center justify-between mb-2">
              <FaRupeeSign className="text-green-500 text-xl" />

            </div>
            <p className="text-2xl font-fredoka text-theme-text-primary">₹{currentData.avgOrderValue}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Avg Order</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center justify-between mb-2">
              <FaStar className="text-yellow-500 text-xl" />

            </div>
            <p className="text-2xl font-fredoka text-theme-text-primary">{currentData.rating}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Rating</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="admin-card p-4 rounded-xl"
          >
            <div className="flex items-center justify-between mb-2">
              <FaUsers className="text-purple-500 text-xl" />
              <FaClock className="text-theme-text-tertiary" />
            </div>
            <p className="text-2xl font-fredoka text-theme-text-primary">{currentData.customers}</p>
            <p className="text-theme-text-secondary font-raleway text-sm">Customers</p>
          </motion.div>
        </div>

        {/* Charts and Top Items */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Performance Chart */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="admin-card p-6 rounded-xl"
          >
            <h3 className="text-lg font-fredoka text-theme-text-primary mb-4">
              {timeRange === 'today' ? 'Hourly Performance' :
                timeRange === 'week' ? 'Daily Performance' : 'Weekly Performance'}
            </h3>
            <div className="space-y-3">
              {(timeRange === 'today' ? currentData.hourlyData :
                timeRange === 'week' ? currentData.dailyData :
                  currentData.weeklyData)?.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-theme-bg-secondary rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-2 h-8 bg-theme-accent-primary rounded"></div>
                        <div>
                          <p className="font-raleway text-theme-text-primary">
                            {item.hour || item.day || item.week}
                          </p>
                          <p className="text-sm text-theme-text-tertiary">{item.orders} orders</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-raleway text-theme-text-primary">₹{item.revenue.toLocaleString()}</p>
                      </div>
                    </div>
                  ))}
            </div>
          </motion.div>

          {/* Top Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="admin-card p-6 rounded-xl"
          >
            <h3 className="text-lg font-fredoka text-theme-text-primary mb-4">Top Performing Items</h3>
            <div className="space-y-4">
              {currentData.topItems?.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-theme-bg-secondary rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-theme-accent-primary rounded-lg flex items-center justify-center">
                      <FaUtensils className="text-white" />
                    </div>
                    <div>
                      <p className="font-raleway text-theme-text-primary">{item.name}</p>
                      <p className="text-sm text-theme-text-tertiary">{item.orders} orders</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-raleway text-theme-text-primary">₹{item.revenue.toLocaleString()}</p>
                    <div className="flex items-center space-x-1">
                      <div className="w-12 bg-theme-bg-primary rounded-full h-2">
                        <div
                          className="bg-theme-accent-primary h-2 rounded-full"
                          style={{ width: `${(item.orders / Math.max(...currentData.topItems.map(i => i.orders))) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Peak Performance Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="admin-card p-6 rounded-xl"
        >
          <h3 className="text-lg font-fredoka text-theme-text-primary mb-4">Performance Insights</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-theme-bg-secondary rounded-lg">
              <FaClock className="text-theme-accent-primary text-2xl mx-auto mb-2" />
              <p className="font-raleway text-theme-text-primary">
                Peak {timeRange === 'today' ? 'Hour' : timeRange === 'week' ? 'Day' : 'Week'}
              </p>
              <p className="text-lg font-fredoka text-theme-accent-primary">
                {currentData.peakHour || currentData.peakDay || currentData.peakWeek}
              </p>
            </div>
            <div className="text-center p-4 bg-theme-bg-secondary rounded-lg">
              <FaRupeeSign className="text-green-500 text-2xl mx-auto mb-2" />
              <p className="font-raleway text-theme-text-primary">Revenue Growth</p>
              <p className={`text-lg font-fredoka ${getTrendColor(currentData.trends?.revenue)}`}>
                {currentData.trends?.revenue > 0 ? '+' : ''}{currentData.trends?.revenue}%
              </p>
            </div>
            <div className="text-center p-4 bg-theme-bg-secondary rounded-lg">
              <FaShoppingCart className="text-blue-500 text-2xl mx-auto mb-2" />
              <p className="font-raleway text-theme-text-primary">Order Growth</p>
              <p className={`text-lg font-fredoka ${getTrendColor(currentData.trends?.orders)}`}>
                {currentData.trends?.orders > 0 ? '+' : ''}{currentData.trends?.orders}%
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </ZoneShopLayout>
  );
};

export default ZoneShopAnalytics;
