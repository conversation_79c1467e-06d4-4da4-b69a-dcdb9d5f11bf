import React from 'react';
import { FaC<PERSON>, FaCheck, FaDownload } from 'react-icons/fa';

export default function QrGeneratedCard({
  title,
  imgSrc,
  url,
  copied = false,
  onCopy = () => {},
  onDownload = () => {},
}) {
  return (
    <div className="border border-theme-border rounded-lg p-4 bg-theme-surface">
      <div className="text-center mb-3">
        <h3 className="font-fredoka text-theme-text-primary text-lg">
          {title}
        </h3>
      </div>

      <div className="flex justify-center mb-3">
        <img
          src={imgSrc}
          alt={title}
          className="w-32 h-32 md:w-48 md:h-48 border border-theme-border rounded"
        />
      </div>

      <div className="space-y-2">
        <div className="text-xs text-theme-text-secondary font-raleway break-all">
          {url}
        </div>

        <div className="flex gap-2">
          <button
            onClick={onCopy}
            className="flex-1 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors flex items-center justify-center"
          >
            {copied ? <FaCheck className="mr-1" /> : <FaCopy className="mr-1" />}
            {copied ? 'Copied!' : 'Copy'}
          </button>
          <button
            onClick={onDownload}
            className="flex-1 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded text-xs transition-colors flex items-center justify-center"
          >
            <FaDownload className="mr-1" />
            Download
          </button>
        </div>
      </div>
    </div>
  );
}

