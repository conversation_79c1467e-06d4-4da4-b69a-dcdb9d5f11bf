import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LocalStorageService from '../../../services/LocalStorageService';
import OTPService from '../../../services/OTPService';
import PasswordManagementModal from '../../common/PasswordManagementModal';
import ImageUpload from '../../common/ImageUpload';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaLock,
  FaUnlock,
  FaKey,
  FaCopy,
  FaMapMarkerAlt,
  FaSearch,
  FaFilter,
  FaDownload,
  FaStore,
  FaUser,
  FaPhone,
  FaEnvelope,
  FaCalendarAlt,
  FaCheckCircle,
  FaTimesCircle,
  FaClock,
  FaBuilding,
  FaUsers,
  FaDollarSign,
  FaChartLine,
  FaRupeeSign
} from 'react-icons/fa';
import SuperAdminLayout from '../SuperAdminLayout';

const FoodZonesManagement = () => {
  const [zones, setZones] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showForm, setShowForm] = useState(false);
  const [editingZone, setEditingZone] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    ownerName: '',
    ownerPhone: '',
    ownerEmail: '',
    maxVendors: '',
    maxTables: 50, // Maximum tables for the zone
    commissionRate: '',
    status: 'active',
    password: '', // Manual password input
    logo: null // Zone logo
  });

  // Password management state
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [passwordTarget, setPasswordTarget] = useState(null);



  useEffect(() => {
    // Load zones from localStorage
    const loadZones = () => {
      const zones = LocalStorageService.getZones();
      setZones(zones);
      setLoading(false);
    };

    loadZones();
  }, []);

  const filteredZones = zones.filter(zone => {
    const matchesSearch = zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      zone.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      zone.ownerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || zone.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.password && !editingZone) {
      alert('Password is required for new zones');
      return;
    }

    if (editingZone) {
      // Update existing zone
      const updateData = { ...formData };
      delete updateData.password; // Don't update password through regular form
      const updatedZone = LocalStorageService.updateZone(editingZone.id, updateData);
      if (updatedZone) {
        const updatedZones = zones.map(z =>
          z.id === editingZone.id ? updatedZone : z
        );
        setZones(updatedZones);
      }
    } else {
      // Add new zone
      const username = formData.name.toLowerCase().replace(/\s+/g, '_').replace(/[^a-z0-9_]/g, '');

      const zoneData = {
        ...formData,
        currentVendors: 0,
        monthlyRevenue: 0,
        totalOrders: 0,
        rating: 0,
        lastActivity: new Date().toISOString(),
        loginCredentials: {
          username: username,
          password: formData.password
        }
      };

      const newZone = LocalStorageService.addZone(zoneData);
      setZones([newZone, ...zones]);
      alert(`Zone created successfully!\nUsername: ${username}\nPassword: ${formData.password}\nPhone: ${formData.ownerPhone}`);
    }

    resetForm();
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      ownerName: '',
      ownerPhone: '',
      ownerEmail: '',
      maxVendors: '',
      maxTables: 50,
      commissionRate: '',
      status: 'active',
      password: '',
      logo: null
    });
    setEditingZone(null);
    setShowForm(false);
  };

  const handleEdit = (zone) => {
    setFormData(zone);
    setEditingZone(zone);
    setShowForm(true);
  };

  const handleDelete = (zoneId) => {
    if (window.confirm('Are you sure you want to delete this food zone?')) {
      const success = LocalStorageService.deleteZone(zoneId);
      if (success) {
        const updatedZones = zones.filter(z => z.id !== zoneId);
        setZones(updatedZones);
      }
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-400 bg-green-500/20';
      case 'pending': return 'text-yellow-400 bg-yellow-500/20';
      case 'inactive': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  const toggleStatus = (zoneId) => {
    const zone = zones.find(z => z.id === zoneId);
    if (zone) {
      const newStatus = zone.status === 'active' ? 'inactive' : 'active';
      const updatedZone = LocalStorageService.updateZone(zoneId, { status: newStatus });
      if (updatedZone) {
        const updatedZones = zones.map(z =>
          z.id === zoneId ? updatedZone : z
        );
        setZones(updatedZones);
      }
    }
  };

  const copyCredentials = (zone) => {
    const credentials = `Zone: ${zone.name}\nUsername: ${zone.loginCredentials.username}\nPassword: ${zone.loginCredentials.password}`;
    navigator.clipboard.writeText(credentials);
    alert('Credentials copied to clipboard!');
  };

  const handlePasswordManagement = (zone) => {
    setPasswordTarget(zone);
    setShowPasswordModal(true);
  };

  const handlePasswordUpdate = async (zoneId, newPassword) => {
    try {
      const updatedZone = LocalStorageService.updateZone(zoneId, {
        loginCredentials: {
          ...passwordTarget.loginCredentials,
          password: newPassword
        }
      });

      if (updatedZone) {
        const updatedZones = zones.map(z =>
          z.id === zoneId ? updatedZone : z
        );
        setZones(updatedZones);
      }
    } catch (error) {
      throw new Error('Failed to update password');
    }
  };

  if (loading) {
    return (
      <SuperAdminLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-theme-text-secondary text-xl">Loading food zones...</div>
        </div>
      </SuperAdminLayout>
    );
  }

  return (
    <SuperAdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-fredoka text-theme-text-secondary mb-2">Zone Owners</h1>
            <p className="text-theme-text-secondary font-raleway text-sm sm:text-base">Manage food zone owners and their multi-vendor operations</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="w-full sm:w-auto bg-accent hover:bg-accent/90 text-white px-4 py-2 rounded-lg font-raleway font-semibold flex items-center justify-center space-x-2"
          >
            <FaPlus />
            <span>Add Food Zone</span>
          </button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary shadow-md"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                <FaMapMarkerAlt className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-theme-text-secondary mb-1">{zones.length}</h3>
            <p className="text-theme-text-secondary font-raleway">Total Zones</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary shadow-md"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaCheckCircle className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-theme-text-secondary mb-1">{zones.filter(z => z.status === 'active').length}</h3>
            <p className="text-theme-text-secondary font-raleway">Active Zones</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary shadow-md"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FaStore className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-theme-text-secondary mb-1">{zones.reduce((sum, z) => sum + z.currentVendors, 0)}</h3>
            <p className="text-theme-text-secondary font-raleway">Total Vendors</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary shadow-md"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-accent rounded-xl flex items-center justify-center">
                <FaRupeeSign className="text-white text-xl" />
              </div>
            </div>
            <h3 className="text-2xl font-fredoka text-theme-text-secondary mb-1">₹{zones.reduce((sum, z) => sum + z.monthlyRevenue, 0).toLocaleString()}</h3>
            <p className="text-theme-text-secondary font-raleway">Monthly Revenue</p>
          </motion.div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-secondary">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-theme-text-secondary" />
              <input
                type="text"
                placeholder="Search zones, cities, or owners..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-white/10 border border-secondary rounded-lg pl-10 pr-4 py-2 text-theme-text-secondary placeholder--secondary focus:outline-none focus:border-accent"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-white/10 border border-secondary rounded-lg px-4 py-2 text-theme-text-secondary focus:outline-none focus:border-accent"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="pending">Pending</option>
              <option value="inactive">Inactive</option>
            </select>

          </div>
        </div>

        {/* Zones List */}
        <div className="bg-white/10 backdrop-blur-lg rounded-2xl border border-secondary overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Zone</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Location</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Owner</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Vendors</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Revenue</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Status</th>
                  <th className="text-left p-4 text-theme-text-secondary font-raleway font-semibold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredZones.map((zone) => (
                  <motion.tr
                    key={zone.id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="border-t border-secondary hover:bg-white/5 transition-colors"
                  >
                    <td className="p-4">
                      <div>
                        <h4 className="text-theme-text-secondary font-raleway font-medium">{zone.name}</h4>
                        <p className="text-theme-text-secondary font-raleway text-sm">{zone.description}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway">{zone.city}, {zone.state}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{zone.address}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway">{zone.ownerName}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{zone.ownerPhone}</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway">{zone.currentVendors}/{zone.maxVendors}</p>
                        <div className="w-full bg-white/20 rounded-full h-2 mt-1">
                          <div
                            className="bg-accent h-2 rounded-full"
                            style={{ width: `${(zone.currentVendors / zone.maxVendors) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </td>
                    <td className="p-4">
                      <div>
                        <p className="text-theme-text-secondary font-raleway">₹{zone.monthlyRevenue.toLocaleString()}</p>
                        <p className="text-theme-text-secondary font-raleway text-sm">{zone.totalOrders} orders</p>
                      </div>
                    </td>
                    <td className="p-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(zone.status)}`}>
                        {zone.status.charAt(0).toUpperCase() + zone.status.slice(1)}
                      </span>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleEdit(zone)}
                          className="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-blue-500/20 transition-colors"
                          title="Edit Zone"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => toggleStatus(zone.id)}
                          className={`p-2 rounded-lg transition-colors ${zone.status === 'active'
                            ? 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
                            : 'text-green-400 hover:text-green-300 hover:bg-green-500/20'
                            }`}
                          title={zone.status === 'active' ? 'Suspend Account' : 'Activate Account'}
                        >
                          {zone.status === 'active' ? <FaLock /> : <FaUnlock />}
                        </button>

                        <button
                          onClick={() => handlePasswordManagement(zone)}
                          className="text-yellow-400 hover:text-yellow-300 p-2 rounded-lg hover:bg-yellow-500/20 transition-colors"
                          title="Change Password"
                        >
                          <FaKey />
                        </button>
                        <button
                          onClick={() => copyCredentials(zone)}
                          className="text-purple-400 hover:text-purple-300 p-2 rounded-lg hover:bg-purple-500/20 transition-colors"
                          title="Copy Credentials"
                        >
                          <FaCopy />
                        </button>
                        <button
                          onClick={() => handleDelete(zone.id)}
                          className="text-red-400 hover:text-red-300 p-2 rounded-lg hover:bg-red-500/20 transition-colors"
                          title="Delete Zone"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Empty State */}
          {filteredZones.length === 0 && (
            <div className="text-center py-12">
              <FaMapMarkerAlt className="text-6xl text-theme-text-tertiary mx-auto mb-4" />
              <h3 className="text-xl font-fredoka text-theme-text-primary mb-2">
                {zones.length === 0 ? 'No Food Zones Added Yet' : 'No Zones Found'}
              </h3>
              <p className="text-theme-text-secondary font-raleway mb-4">
                {zones.length === 0
                  ? 'Start by adding your first food zone to the platform'
                  : 'No zones match your current search criteria'
                }
              </p>
              {zones.length === 0 && (
                <button
                  onClick={() => setShowForm(true)}
                  className="btn-primary px-6 py-3 rounded-lg font-raleway font-semibold"
                >
                  Add First Zone
                </button>
              )}
            </div>
          )}
        </div>

        {/* Add/Edit Zone Modal */}
        <AnimatePresence>
          {showForm && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/20 admin-card backdrop-blur-sm z-50 flex items-center justify-center p-4"
              onClick={(e) => e.target === e.currentTarget && resetForm()}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className=" bg-primary backdrop-blur-xl border border-white/20 rounded-2xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto"
              >
                <h2 className="text-2xl font-raleway font-semibold text-primary mb-6">
                  {editingZone ? 'Edit Food Zone' : 'Add New Food Zone'}
                </h2>

                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Zone Name</label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter Zone Name'
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Max Vendors</label>
                      <input
                        type="number"
                        value={formData.maxVendors}
                        onChange={(e) => setFormData({ ...formData, maxVendors: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter Max Vendors'
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Maximum Tables (QR Codes) *</label>
                    <input
                      type="number"
                      min="1"
                      max="500"
                      value={formData.maxTables}
                      onChange={(e) => setFormData({ ...formData, maxTables: parseInt(e.target.value) || 1 })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      placeholder="Enter maximum number of tables"
                      required
                    />
                    <p className="text-secondary text-sm mt-1">Maximum number of tables/QR codes for the entire zone</p>
                  </div>

                  {/* Logo Upload */}
                  <div>
                    <ImageUpload
                      currentImage={formData.logo}
                      onImageChange={(imageUrl, file) => {
                        setFormData({ ...formData, logo: imageUrl });
                      }}
                      label="Zone Logo"
                      size="large"
                      shape="rounded"
                      className="mb-4"
                    />
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      rows="3"
                      required
                      placeholder='Enter Description'
                    />
                  </div>

                  <div>
                    <label className="block text-primary font-raleway mb-2">Address</label>
                    <input
                      type="text"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                      required
                      placeholder='Enter Address'
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">City</label>
                      <input
                        type="text"
                        value={formData.city}
                        onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter City'
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">State</label>
                      <input
                        type="text"
                        value={formData.state}
                        onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter State'
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Zip Code</label>
                      <input
                        type="text"
                        value={formData.zipCode}
                        onChange={(e) => setFormData({ ...formData, zipCode: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter ZIP Code'
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Owner Name</label>
                      <input
                        type="text"
                        value={formData.ownerName}
                        onChange={(e) => setFormData({ ...formData, ownerName: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter Owner Name'
                      />
                    </div>
                    <div>
                      <label className="block text-primary font-raleway mb-2">Owner Phone</label>
                      <input
                        type="tel"
                        value={formData.ownerPhone}
                        onChange={(e) => setFormData({ ...formData, ownerPhone: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter Owner Phone'
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-primary font-raleway mb-2">Owner Email</label>
                      <input
                        type="email"
                        value={formData.ownerEmail}
                        onChange={(e) => setFormData({ ...formData, ownerEmail: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        required
                        placeholder='Enter Owner Email'
                      />
                    </div>

                  </div>

                  {/* Password field - only for new zones */}
                  {!editingZone && (
                    <div>
                      <label className="block text-primary font-raleway mb-2">Login Password *</label>
                      <input
                        type="password"
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                        className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                        placeholder="Enter secure password for zone admin login"
                        required
                        minLength="6"
                      />
                      <p className="text-primary text-sm mt-1">This password will be used for zone admin login</p>
                    </div>
                  )}

                  <div>
                    <label className="block text-primary font-raleway mb-2">Status</label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                      className="w-full bg-transparent border border-primary rounded-lg px-4 py-2 text-primary placeholder-primary focus:outline-none focus:border-accent"
                    >
                      <option value="active">Active</option>
                      <option value="pending">Pending</option>
                      <option value="inactive">Inactive</option>
                    </select>
                  </div>

                  <div className="flex justify-end space-x-4 pt-4">
                    <button
                      type="button"
                      onClick={resetForm}
                      className="px-6 py-2 bg-transparent text-primary hover:bg-gray-400 hover:text-white  text-primary rounded-lg font-raleway transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-accent hover:bg-accent/90 text-white rounded-lg font-raleway transition-colors"
                    >
                      {editingZone ? 'Update Zone' : 'Create Zone'}
                    </button>
                  </div>
                </form>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Password Management Modal */}
        <PasswordManagementModal
          isOpen={showPasswordModal}
          onClose={() => {
            setShowPasswordModal(false);
            setPasswordTarget(null);
          }}
          entity={passwordTarget}
          entityType="zone"
          onPasswordUpdate={handlePasswordUpdate}
          currentUserRole="superadmin"
        />
      </div>
    </SuperAdminLayout>
  );
};

export default FoodZonesManagement;
