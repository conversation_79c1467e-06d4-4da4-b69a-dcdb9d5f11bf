import { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  FaRupeeSign,
  FaShoppingCart,
  FaStar,
  FaUtensils,
  FaChartLine,
  FaClock,
  FaUsers,
  FaExclamationTriangle,
  FaCheckCircle,
  FaEye,
  FaEdit
} from 'react-icons/fa';
import ZoneShopLayout from './ZoneShopLayout';
import AnalyticsService from '../../services/AnalyticsService';

const ZoneShopDashboard = () => {
  const { zoneId, shopId } = useParams();
  const [stats, setStats] = useState({});
  const [recentOrders, setRecentOrders] = useState([]);
  const [popularItems, setPopularItems] = useState([]);
  const [loading, setLoading] = useState(true);

  const mockRecentOrders = [
    {
      id: 'ORD-001',
      customerName: '<PERSON> Do<PERSON>',
      items: ['Margherita Pizza', 'Coke'],
      total: 24.99,
      status: 'preparing',
      time: '2 min ago',
      table: 'T-15'
    },
    {
      id: 'ORD-002',
      customerName: '<PERSON>',
      items: ['Pepperoni Pizza', 'Garlic Bread'],
      total: 32.50,
      status: 'ready',
      time: '5 min ago',
      table: 'T-08'
    },
    {
      id: 'ORD-003',
      customerName: 'Mike Wilson',
      items: ['Veggie Pizza', 'Sprite'],
      total: 28.75,
      status: 'completed',
      time: '12 min ago',
      table: 'T-22'
    }
  ];

  const mockPopularItems = [
    { name: 'Margherita Pizza', orders: 15, revenue: 299.85 },
    { name: 'Pepperoni Pizza', orders: 12, revenue: 359.88 },
    { name: 'Veggie Supreme', orders: 8, revenue: 239.92 },
    { name: 'Garlic Bread', orders: 18, revenue: 89.82 }
  ];

  useEffect(() => {
    const loadDashboardData = () => {
      try {
        setLoading(true);

        // Get analytics data from AnalyticsService
        const analyticsData = AnalyticsService.getShopAnalytics(shopId);

        setStats({
          todayRevenue: analyticsData.totalRevenue || 0,
          todayOrders: analyticsData.totalOrders || 0,
          avgRating: analyticsData.avgRating || 0,
          menuItems: analyticsData.menuItems || 0,
          weeklyGrowth: analyticsData.revenueChange || 0,
          pendingOrders: 0, // This would come from order management
          completedToday: analyticsData.totalOrders || 0,
          lowStockItems: 0 // This would come from inventory management
        });

        setRecentOrders(analyticsData.recentOrders || []);
        setPopularItems(analyticsData.popularItems || []);
        setLoading(false);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Set default empty data
        setStats({
          todayRevenue: 0,
          todayOrders: 0,
          avgRating: 0,
          menuItems: 0,
          weeklyGrowth: 0,
          pendingOrders: 0,
          completedToday: 0,
          lowStockItems: 0
        });
        setRecentOrders([]);
        setPopularItems([]);
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [shopId]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'preparing': return 'status-warning bg-status-warning-light';
      case 'ready': return 'status-success bg-status-success-light';
      case 'completed': return 'status-info bg-status-info-light';
      default: return 'text-theme-text-tertiary bg-theme-bg-secondary';
    }
  };

  if (loading) {
    return (
      <ZoneShopLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="w-16 h-16 border-4 border-theme-accent-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
            <p className="text-theme-text-secondary">Loading dashboard...</p>
          </div>
        </div>
      </ZoneShopLayout>
    );
  }

  return (
    <ZoneShopLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-fredoka text-primary mb-2 ">Shop Dashboard</h1>
          <p className="text-primary/70 font-raleway">Welcome back! Here's your shop overview for today.</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                <FaRupeeSign className="text-primary text-xl" />
              </div>
              <span className="text-green-400 text-sm font-raleway">+{stats.weeklyGrowth}%</span>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">₹{stats.todayRevenue}</h3>
            <p className="text-primary/70 font-raleway">Today's Revenue</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                <FaShoppingCart className="text-primary text-xl" />
              </div>
              <span className="text-yellow-400 text-sm font-raleway">{stats.pendingOrders} pending</span>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{stats.todayOrders}</h3>
            <p className="text-primary/70 font-raleway">Today's Orders</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center">
                <FaStar className="text-primary text-xl" />
              </div>
              <span className="text-green-400 text-sm font-raleway">Excellent</span>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{stats.avgRating}</h3>
            <p className="text-primary/70 font-raleway">Average Rating</p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-4 sm:p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                <FaUtensils className="text-primary text-xl" />
              </div>
              <span className="text-red-400 text-sm font-raleway">{stats.lowStockItems} low stock</span>
            </div>
            <h3 className="text-2xl font-fredoka text-primary mb-1">{stats.menuItems}</h3>
            <p className="text-primary/70 font-raleway">Menu Items</p>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Orders */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-fredoka text-primary">Recent Orders</h2>
              <button className="text-accent hover:text-accent/80 font-raleway text-sm">View All</button>
            </div>

            <div className="space-y-4">
              {recentOrders.map((order) => (
                <div key={order.id} className="bg-primary/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="text-primary font-raleway font-medium">{order.customerName}</h4>
                      <p className="text-primary/60 font-raleway text-sm">Table {order.table} • {order.time}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-raleway font-medium ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-primary/70 font-raleway text-sm">{order.items.join(', ')}</p>
                    <p className="text-primary font-raleway font-medium">₹{order.total}</p>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Popular Items */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-primary/10 backdrop-blur-lg rounded-2xl p-6 border border-primary/10"
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-fredoka text-primary">Popular Items Today</h2>
              <button className="text-accent hover:text-accent/80 font-raleway text-sm">Manage Menu</button>
            </div>

            <div className="space-y-4">
              {popularItems.map((item, index) => (
                <div key={item.name} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-accent/20 rounded-lg flex items-center justify-center">
                      <span className="text-accent font-raleway font-bold text-sm">{index + 1}</span>
                    </div>
                    <div>
                      <h4 className="text-primary font-raleway font-medium">{item.name}</h4>
                      <p className="text-primary/60 font-raleway text-sm">{item.orders} orders</p>
                    </div>
                  </div>
                  <p className="text-primary font-raleway font-medium">₹{item.revenue}</p>
                </div>
              ))}
            </div>
          </motion.div>
        </div>


      </div>
    </ZoneShopLayout>
  );
};

export default ZoneShopDashboard;
