// Subscription plan definitions for restaurants and zones

export const RESTAURANT_PLANS = {
  basic: {
    key: 'basic',
    label: 'Basic',
    planType: 'restaurant',
    maxTables: 1,
    features: {
      crudMenu: true,
      branding: false,
      analytics: false,
    },
  },
  medium: {
    key: 'medium',
    label: 'Medium',
    planType: 'restaurant',
    maxTables: 3,
    features: {
      crudMenu: true,
      branding: true,
      analytics: false,
    },
  },
  advanced: {
    key: 'advanced',
    label: 'Advanced',
    planType: 'restaurant',
    maxTables: 8,
    features: {
      crudMenu: true,
      branding: true,
      analytics: true,
    },
  },
};

export const ZONE_PLANS = {
  basic: {
    key: 'basic',
    label: 'Basic',
    planType: 'zone',
    maxTables: 2,
    maxVendors: 2,
    features: {
      superAdminCRUD: false,
      splitCart: false,
      analytics: false,
    },
  },
  medium: {
    key: 'medium',
    label: 'Medium',
    planType: 'zone',
    maxTables: 5,
    maxVendors: 4,
    features: {
      superAdminCRUD: false,
      splitCart: true,
      analytics: false,
    },
  },
  advanced: {
    key: 'advanced',
    label: 'Advanced',
    planType: 'zone',
    maxTables: null, // Custom
    maxVendors: null, // Custom
    features: {
      superAdminCRUD: true,
      splitCart: true,
      analytics: true,
    },
  },
};

export function getDefaultPlanForType(planType) {
  return planType === 'zone' ? ZONE_PLANS.basic : RESTAURANT_PLANS.basic;
}

export function resolvePlanMetadata({ planKey, planType, custom = {} }) {
  const source = planType === 'zone' ? ZONE_PLANS : RESTAURANT_PLANS;
  const base = source[planKey] || getDefaultPlanForType(planType);
  return {
    plan: base.key,
    planType: base.planType,
    maxTables: base.maxTables ?? custom.maxTables ?? 0,
    maxVendors: base.planType === 'zone' ? (base.maxVendors ?? custom.maxVendors ?? 0) : undefined,
    features: base.features,
    status: 'active',
    startedAt: new Date().toISOString(),
  };
}

