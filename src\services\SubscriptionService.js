class SubscriptionService {
  static KEY = 'tableserve_subscription';

  static get() {
    try {
      const raw = localStorage.getItem(this.KEY);
      return raw ? JSON.parse(raw) : null;
    } catch (e) {
      return null;
    }
  }

  static set(metadata) {
    try {
      localStorage.setItem(this.KEY, JSON.stringify(metadata));
      return true;
    } catch (e) {
      return false;
    }
  }

  static clear() {
    localStorage.removeItem(this.KEY);
  }
}

export default SubscriptionService;

